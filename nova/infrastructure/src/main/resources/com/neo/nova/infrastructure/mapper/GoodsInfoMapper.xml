<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.GoodsInfoMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.GoodsInfo">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="code" column="code" />
            <result property="outId" column="outId" />
            <result property="outType" column="outType" />
            <result property="name" column="name" />
            <result property="mnemoCode" column="mnemoCode" />
            <result property="goodsTypeId" column="goodsTypeId" />
            <result property="shipperId" column="shipperId" />
            <result property="spec" column="spec" />
            <result property="unit" column="unit" />
            <result property="jWeight" column="jWeight" />
            <result property="mWeight" column="mWeight" />
            <result property="status" column="status" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,code,outId,outType,name,
        mnemoCode,goodsTypeId,shipperId,spec,unit,
        jWeight,mWeight,status,createdBy,created,
        updatedBy,updated,isDeleted
    </sql>
</mapper>
