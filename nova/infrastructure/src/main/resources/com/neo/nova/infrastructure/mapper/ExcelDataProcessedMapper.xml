<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.ExcelDataProcessedMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.ExcelDataProcessed">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="dataSourceConfigId" column="dataSourceConfigId" />
            <result property="customerNameOriginal" column="customerNameOriginal" />
            <result property="customerId" column="customerId" />
            <result property="customerName" column="customerName" />
            <result property="customerCode" column="customerCode" />
            <result property="itemNameOriginal" column="itemNameOriginal" />
            <result property="goodsId" column="goodsId" />
            <result property="goodsName" column="goodsName" />
            <result property="startDate" column="startDate" />
            <result property="endDate" column="endDate" />
            <result property="salesQuantity" column="salesQuantity" />
            <result property="salesUnit" column="salesUnit" />
            <result property="salesPrice" column="salesPrice" />
            <result property="salesAmount" column="salesAmount" />
            <result property="costPrice" column="costPrice" />
            <result property="costAmount" column="costAmount" />
            <result property="confirmStatus" column="confirmStatus" />
            <result property="matchStatus" column="matchStatus" />
            <result property="extendField1" column="extendField1"/>
            <result property="extendField2" column="extendField2"/>
            <result property="created" column="created" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,dataSourceConfigId,customerNameOriginal,
        customerId,customerName,customerCode,itemNameOriginal,goodsId,
        goodsName,startDate,endDate,salesQuantity,
        salesUnit,salesPrice,salesAmount,costPrice,costAmount,confirmStatus,matchStatus,
          extendField1,extendField2,created,updated,isDeleted
    </sql>
    <insert id="_insertBatch" parameterType="java.util.List">
        insert into ExcelDataProcessed (tenantId,dataSourceConfigId,customerNameOriginal,
        customerId,customerName,customerCode,itemNameOriginal,goodsId,
        goodsName,startDate,endDate,salesQuantity,
        salesUnit,salesPrice,salesAmount,costPrice,costAmount,confirmStatus,matchStatus,
        extendField1,extendField2,created,updated,isDeleted) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.tenantId},#{item.dataSourceConfigId},#{item.customerNameOriginal},
            #{item.customerId},#{item.customerName},#{item.customerCode},#{item.itemNameOriginal},#{item.goodsId},
            #{item.goodsName},#{item.startDate},#{item.endDate},#{item.salesQuantity},
            #{item.salesUnit},#{item.salesPrice},#{item.salesAmount},#{item.costPrice},#{item.costAmount},#{item.confirmStatus},#{item.matchStatus},
            #{item.extendField1},#{item.extendField2},#{item.created},#{item.updated},#{item.isDeleted})
        </foreach>
    </insert>
</mapper>
