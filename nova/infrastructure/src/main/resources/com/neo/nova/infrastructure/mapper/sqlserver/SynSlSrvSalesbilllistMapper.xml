<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesbilllistMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mateid" column="MateID" jdbcType="INTEGER"/>
        <result property="sellprice" column="SellPrice" jdbcType="NUMERIC"/>
        <result property="qty" column="Qty" jdbcType="NUMERIC"/>
        <result property="delivqty" column="DelivQty" jdbcType="NUMERIC"/>
        <result property="billmoney" column="BillMoney" jdbcType="NUMERIC"/>
        <result property="delivmoney" column="DelivMoney" jdbcType="NUMERIC"/>
        <result property="salesbillid" column="SALESBILLID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,MateID,SellPrice,
        Qty,DelivQty,BillMoney,
        DelivMoney,SALESBILLID
    </sql>
</mapper>
