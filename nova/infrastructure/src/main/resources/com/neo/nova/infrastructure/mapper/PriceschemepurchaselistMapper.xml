<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.PriceschemepurchaselistMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.Priceschemepurchaselist">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="custTypeID" column="custTypeID" jdbcType="INTEGER"/>
        <result property="matelID" column="matelID" jdbcType="INTEGER"/>
        <result property="sellPrice" column="sellPrice" jdbcType="DECIMAL"/>
        <result property="outId" column="outId" jdbcType="VARCHAR"/>
        <result property="outType" column="outType" jdbcType="VARCHAR"/>
        <result property="printPrice" column="printPrice" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,custTypeID,matelID,outId,outType,
        sellPrice,printPrice
    </sql>
</mapper>
