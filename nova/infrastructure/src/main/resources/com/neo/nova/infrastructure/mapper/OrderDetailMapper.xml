<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.OrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.OrderDetail">
            <id property="id" column="id" />
            <result property="orderId" column="orderId" />
            <result property="customerId" column="customerId" />
            <result property="goodsId" column="goodsId" />
            <result property="salePrice" column="salePrice" />
            <result property="salesQty" column="salesQty" />
            <result property="delivyQty" column="delivyQty" />
            <result property="billMoney" column="billMoney" />
            <result property="delivMoney" column="delivMoney" />
            <result property="created" column="created" />
            <result property="updated" column="updated" />
            <result property="deleted" column="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,orderId,customerId,goodsId,salePrice,salesQty,
        delivyQty,billMoney,delivMoney,created,updated,
        deleted
    </sql>
</mapper>
