<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver.SynSlSrvSalesBillMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="billno" column="BILLNO" jdbcType="VARCHAR"/>
        <result property="billtype" column="BILLTYPE" jdbcType="CHAR"/>
        <result property="custid" column="CUSTID" jdbcType="INTEGER"/>
        <result property="delivdate" column="DELIVYDATE" jdbcType="TIMESTAMP"/>
        <result property="billstate" column="BILLSTATE" jdbcType="CHAR"/>
        <result property="synid" column="SYNID" jdbcType="INTEGER"/>
        <result property="printtime" column="PRINTTIME" jdbcType="INTEGER"/>
        <result property="billfrom" column="BILLFROM" jdbcType="CHAR"/>
        <result property="billdate" column="BILLDATE" jdbcType="TIMESTAMP"/>
        <result property="billmoney" column="BILLMONEY" jdbcType="NUMERIC"/>
        <result property="verifyperson" column="VerifyPerson" jdbcType="VARCHAR"/>
        <result property="verifydate" column="VerifyDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,BILLNO,BILLTYPE,
        CUSTID,DELIVYDATE,BILLSTATE,
        SYNID,PRINTTIME,BILLFROM,
        BILLDATE,BILLMONEY,VerifyPerson,
        VerifyDate
    </sql>
</mapper>
