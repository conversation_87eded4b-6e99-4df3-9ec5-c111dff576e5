<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.st_trd_customer_produce_month_infoMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.st_trd_customer_produce_month_info">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="produceId" column="produceId" />
            <result property="produceName" column="produceName" />
            <result property="produceCode" column="produceCode" />
            <result property="produceTypeId" column="produceTypeId" />
            <result property="customerId" column="customerId" />
            <result property="customerName" column="customerName" />
            <result property="customerMnemoCode" column="customerMnemoCode" />
            <result property="customerCode" column="customerCode" />
            <result property="customerTypeId" column="customerTypeId" />
            <result property="customerLevel" column="customerLevel" />
            <result property="channel" column="channel" />
            <result property="supermarketAreaId" column="supermarketAreaId" />
            <result property="salesRegionId" column="salesRegionId" />
            <result property="adminRegionId" column="adminRegionId" />
            <result property="salesId" column="salesId" />
            <result property="salesCode" column="salesCode" />
            <result property="salesName" column="salesName" />
            <result property="amount" column="amount" />
            <result property="delivAmount" column="delivAmount" />
            <result property="salesQty" column="salesQty" />
            <result property="delivyQty" column="delivyQty" />
            <result property="billMoney" column="billMoney" />
            <result property="costAmount" column="costAmount" />
            <result property="visitDate" column="visit_date" />
            <result property="extra" column="extra" />
            <result property="created" column="created" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,tenantId,produceId,produceName,produceCode,produceTypeId,
        customerId,customerName,customerMnemoCode,customerCode,customerTypeId,
        customerLevel,channel,supermarketAreaId,salesRegionId,adminRegionId,
        salesId,salesCode,salesName,amount,delivAmount,salesQty,
        delivyQty,billMoney,costAmount,visit_date,extra,created,updated,isDeleted
    </sql>
</mapper>
