<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.sqlserver2.SynIvPamMaterial2Mapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.sqlserver.SynIvPamMaterial">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mateno" column="MateNo" jdbcType="INTEGER"/>
        <result property="matename" column="MateName" jdbcType="VARCHAR"/>
        <result property="mnemocode" column="MnemoCode" jdbcType="VARCHAR"/>
        <result property="ordercode" column="OrderCode" jdbcType="INTEGER"/>
        <result property="matetypeid" column="MateTypeID" jdbcType="INTEGER"/>
        <result property="shipperid" column="ShipperID" jdbcType="INTEGER"/>
        <result property="spec" column="Spec" jdbcType="VARCHAR"/>
        <result property="unit" column="Unit" jdbcType="VARCHAR"/>
        <result property="jweight" column="JWeight" jdbcType="DECIMAL"/>
        <result property="mweight" column="MWeight" jdbcType="DECIMAL"/>
        <result property="ustate" column="UState" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,MateNo,MateName,
        MnemoCode,OrderCode,MateTypeID,
        ShipperID,Spec,Unit,
        JWeight,MWeight,UState
    </sql>
</mapper>
