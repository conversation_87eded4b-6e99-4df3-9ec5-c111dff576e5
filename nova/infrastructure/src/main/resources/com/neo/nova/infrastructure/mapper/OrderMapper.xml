<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.OrderMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.Order">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="code" column="code" />
            <result property="outId" column="outId" />
            <result property="outType" column="outType" />
            <result property="orderType" column="orderType" />
            <result property="customerId" column="customerId" />
            <result property="delivyDate" column="delivyDate" />
            <result property="status" column="status" />
            <result property="synId" column="synId" />
            <result property="printTime" column="printTime" />
            <result property="orderFrom" column="orderFrom" />
            <result property="orderTime" column="orderTime" />
            <result property="amount" column="amount" />
            <result property="verifyPerson" column="verifyPerson" />
            <result property="verifyDate" column="verifyDate" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,code,outId,outType,orderType,
        customerId,delivyDate,status,synId,printTime,
        orderFrom,orderTime,amount,verifyPerson,verifyDate,
        createdBy,created,updatedBy,updated,isDeleted
    </sql>
</mapper>
