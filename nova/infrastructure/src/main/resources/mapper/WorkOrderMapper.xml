<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.WorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.WorkOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenantId" jdbcType="BIGINT"/>
            <result property="creatorId" column="creatorId" jdbcType="BIGINT"/>
            <result property="executorId" column="executorId" jdbcType="BIGINT"/>
            <result property="updaterId" column="updaterId" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="priority" column="priority" jdbcType="TINYINT"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="workOrderStartTime" column="workOrderStartTime" jdbcType="TIMESTAMP"/>
            <result property="workOrderEndTime" column="workOrderEndTime" jdbcType="TIMESTAMP"/>
            <result property="created" column="created" jdbcType="TIMESTAMP"/>
            <result property="updated" column="updated" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="subType" column="subType" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,creatorId,
        executorId,updaterId,title,
        description,status,priority,
        type,workOrderStartTime,workOrderEndTime,
        created,updated,deleted,
        subType
    </sql>
</mapper>
