<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neo.nova.infrastructure.mapper.MetricMapper">

    <resultMap id="BaseResultMap" type="com.neo.nova.domain.entity.Metric">
            <id property="id" column="id" />
            <result property="tenantId" column="tenantId" />
            <result property="metricDataType" column="metricDataType" />
            <result property="metricCode" column="metricCode" />
            <result property="metricCodeId" column="metricCodeId" />
            <result property="metricName" column="metricName" />
            <result property="unit" column="unit" />
            <result property="calcType" column="calcType" />
            <result property="description" column="description" />
            <result property="createdBy" column="createdBy" />
            <result property="created" column="created" />
            <result property="updatedBy" column="updatedBy" />
            <result property="updated" column="updated" />
            <result property="isDeleted" column="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenantId,metricDataType,metricCode,metricCodeId,metricName,unit,
        calcType,description,createdBy,created,updatedBy,
        updated,isDeleted
    </sql>
</mapper>
