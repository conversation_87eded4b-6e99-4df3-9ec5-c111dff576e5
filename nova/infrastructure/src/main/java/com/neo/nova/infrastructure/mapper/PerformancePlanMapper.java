package com.neo.nova.infrastructure.mapper;

import com.neo.nova.domain.entity.PerformancePlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【PerformancePlan(业绩计划表)】的数据库操作Mapper
* @createDate 2025-07-03 14:54:38
* @Entity com.neo.nova.domain.entity.PerformancePlan
*/
public interface PerformancePlanMapper extends BaseMapper<PerformancePlan> {

    List<PerformancePlan> selectAll();

    PerformancePlan selectByPlanId(Long planId);

}




