package com.neo.nova.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.domain.entity.ExcelDataProcessed;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ExcelDataProcessed(Excel加工后数据表 - 存储匹配后的数据)】的数据库操作Mapper
* @createDate 2025-07-24 21:20:42
* @Entity com.neo.nova.infrastructure.domain.ExcelDataProcessed
*/
public interface ExcelDataProcessedMapper extends BaseMapper<ExcelDataProcessed> {


    int _insertBatch(@Param("list") List<ExcelDataProcessed> list);
}




