package com.neo.nova.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.nova.infrastructure.mapper.WorkOrderMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【WorkOrder(工单主表)】的数据库操作Service实现
* @createDate 2025-07-10 12:06:35
*/
@Service
public class WorkOrderRepositoryImpl extends ServiceImpl<WorkOrderMapper, WorkOrder>
    implements IWorkOrderRepository{

}




