package com.neo.nova.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.neo.nova.app.service.SupermarketSalesService;
import com.neo.nova.domain.entity.SupermarketSalesMCPData;
import com.neo.nova.domain.gateway.SupermarketSalesMCPDataRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/17
 **/
@Slf4j
@Service
public class SupermarketSalesServiceImpl implements SupermarketSalesService {

    @Resource
    private SupermarketSalesMCPDataRepository supermarketSalesMCPDataRepository;

    @Override
    public List<SupermarketSalesMCPData> list(Long tenantId, Long userId, Long conversationId) {
        if (userId == null || conversationId == null) {
            return List.of();
        }
        LambdaQueryWrapper<SupermarketSalesMCPData> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(SupermarketSalesMCPData::getTenantId, tenantId);
//        queryWrapper.eq(SupermarketSalesMCPData::getUserId, userId);
        queryWrapper.eq(SupermarketSalesMCPData::getConversationId, conversationId);
        queryWrapper.eq(SupermarketSalesMCPData::getIsDeleted, 0);
        return supermarketSalesMCPDataRepository.list(queryWrapper);
    }

    @Override
    public boolean batchUpdateByIds(List<Long> ids, Long tenantId, Long updateUserId, SupermarketSalesMCPData supermarketSalesMCPData) {
        if (supermarketSalesMCPData == null || ids == null || ids.isEmpty()) {
            log.error("参数不能为空, supermarketSalesMCPData: {}, ids: {}", supermarketSalesMCPData, ids);
            return false;
        }
        LambdaUpdateWrapper<SupermarketSalesMCPData> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SupermarketSalesMCPData::getId, ids);
//        updateWrapper.in(SupermarketSalesMCPData::getTenantId, tenantId);

//        supermarketSalesMCPData.setUpdatedBy(updateUserId);
        supermarketSalesMCPData.setUpdated(System.currentTimeMillis() / 1000);

        return supermarketSalesMCPDataRepository.update(supermarketSalesMCPData, updateWrapper);
    }

}
