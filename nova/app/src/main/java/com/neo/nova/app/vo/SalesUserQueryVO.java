package com.neo.nova.app.vo;

import com.neo.api.PageReqDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SalesUserQueryVO extends PageReqDTO {

    private List< Long> salesUserIds;

    private List< Long> deptIds;

    private Long customerId;

    private BigDecimal monthStart;

    private BigDecimal monthEnd;

    private BigDecimal yearStart;

    private BigDecimal yearEnd;

    private List< Integer> status;

    private Long tenantId;


}
