package com.neo.nova.app.service.impl;/*
 *
 *
 *                                                    __----~~~~~~~~~~~------___
 *                                   .  .   ~~//====......          __--~ ~~
 *                   -.            \_|//     |||\\  ~~~~~~::::... /~
 *                ___-==_       _-~o~  \/    |||  \\            _/~~-
 *        __---~~~.==~||\=_    -_--~/_-~|-   |\\   \\        _/~
 *    _-~~     .=~    |  \\-_    '-~7  /-   /  ||    \      /
 *  .~       .~       |   \\ -_    /  /-   /   ||      \   /
 * /  ____  /         |     \\ ~-_/  /|- _/   .||       \ /
 * |~~    ~~|--~~~~--_ \     ~==-/   | \~--===~~        .\
 *          '         ~-|      /|    |-~\~~       __--~~
 *                      |-~~-_/ |    |   ~\_   _-~            /\
 *                           /  \     \__   \/~                \__
 *                       _--~ _/ | .-~~____--~-/                  ~~==.
 *                      ((->/~   '.|||' -_|    ~~-/ ,              . _||
 *                                 -_     ~\      ~~---l__i__i__i--~~_/
 *                                 _-~-__   ~)  \--______________--~~
 *                               //.-~~~-~_--~- |-------~~~~~~~~
 *                                      //.-~~~--\
 *                               神兽保佑
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 15:16
 */

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.action.actionType.VisitingRecordEnum;
import com.neo.nova.app.action.config.ActionFlowConfig;
import com.neo.nova.app.action.config.FlowConfig;
import com.neo.nova.app.action.enums.*;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.actionType.ActionSubTypeEnums;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.service.*;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.dto.WorkOrderDetailDto;
import com.neo.nova.domain.dto.WorkOrderDto;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.entity.WorkOrderEvent;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderEventRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.nova.infrastructure.service.impl.WorkOrderRepositoryImpl;
import com.neo.session.SessionContextHolder;
import com.neo.user.client.tenant.api.UserTenantService;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WorkOrderOperationServiceImpl implements WorkOrderOperationService {

    @Resource
    private IWorkOrderDetailRepository iworkOrderDetailRepository;

    @Resource
    private IWorkOrderRepository iWorkOrderRepository;

    @Resource
    private IWorkOrderEventRepository iWorkOrderEventRepository;

    @Resource
    private WorkOrderRemarksService workOrderRemarksService;

    @Resource
    private UserService userService;

    @Resource
    private MetricService metricService;

    @Resource
    UserTenantService userTenantService;

    @Resource
    DepartmentRemoteService departmentRemoteService;

    @Resource
    CustomerMetricService customerMetricService;
    private WorkOrderRepositoryImpl workOrderRepositoryImpl;


    /**
     * 根据id查询工单信息
     *
     * @param id
     * @return
     */
    @Override
    public WorkOrderDto getWorkOrderById(Long id) {
        if (id != null) {
            WorkOrder workOrder = iWorkOrderRepository.getById(id);
            return WorkOrderDto.covertDto(workOrder);
        }
        return null;
    }

    /**
     * 根据工单id查询工单详情
     *
     * @param workOrderId
     * @return
     */
    @Override
    public List<WorkOrderDetailDto> getWorkOrderDetailByWorkOrderId(Long workOrderId) {
        if (workOrderId != null) {
            LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderDetail::getWorkOrderId, workOrderId);
            queryWrapper.orderByAsc(WorkOrderDetail::getSort);
            List<WorkOrderDetail> res = iworkOrderDetailRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(res)) {
                return res.stream().map(WorkOrderDetailDto::covertDto).collect(Collectors.toList());
            }
        }
        return List.of();
    }

    /**
     * 根据客户id查询工单详情
     *
     * @return
     */
    @Override
    public List<WorkOrderDetailDto> getWorkOrderDetailByCustomerId(Long CustomerId) {
        if (CustomerId != null) {
            LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderDetail::getBizId, CustomerId);
            queryWrapper.orderByAsc(WorkOrderDetail::getSort);
            List<WorkOrderDetail> res = iworkOrderDetailRepository.list(queryWrapper);
            if (!CollectionUtils.isEmpty(res)) {
                return res.stream().map(WorkOrderDetailDto::covertDto).collect(Collectors.toList());
            }
        }
        return List.of();
    }

    /**
     * 查询工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @Override
    public PageResponse<WorkOrderListItemDTO> queryWorkOrders(WorkOrderQueryRequest request) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WorkOrder> queryWrapper = buildQueryWrapper(request);

            // 分页查询
            Page<WorkOrder> page = new Page<>(request.getPageIndex(), request.getPageSize());
            IPage<WorkOrder> workOrderPage = iWorkOrderRepository.page(page, queryWrapper);

            // 转换为DTO
            List<WorkOrderListItemDTO> workOrderDTOs = convertToListItemDTOs(request,workOrderPage.getRecords());

            return PageResponse.of(
                    workOrderDTOs,
                    workOrderPage.getTotal(),
                    request.getPageSize(),
                    request.getPageIndex()
            );

        } catch (Exception e) {
            log.error("查询工单列表失败", e);
            return PageResponse.buildFailure("QUERY_ERROR", "查询工单列表失败: " + e.getMessage());
        }
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<WorkOrder> buildQueryWrapper(WorkOrderQueryRequest request) {
        LambdaQueryWrapper<WorkOrder> queryWrapper = new LambdaQueryWrapper<>();

        // 排除已删除的记录
        queryWrapper.eq(WorkOrder::getDeleted, 0);

        // 状态筛选
        if (request.getStatus() != null) {
            queryWrapper.eq(WorkOrder::getStatus, request.getStatus());
        }

        // 加急工单筛选
        if (request.getUrgentOnly() != null && request.getUrgentOnly()) {
            queryWrapper.eq(WorkOrder::getPriority, WorkOrderPriorityEnums.URGENT.getCode());
        }

        // 执行人筛选
        if (request.getExecutorId() != null) {
            queryWrapper.eq(WorkOrder::getExecutorId, request.getExecutorId());
        }

        // 类型筛选
        if (request.getType() != null) {
            queryWrapper.eq(WorkOrder::getType, request.getType());
        }

        // 直属下属筛选
        if (request.getOnlySub() != null) {
            //queryWrapper.eq(WorkOrder::getType, request.getType());
            List<Long> subUserIds = departmentRemoteService.getSubUserIdsByUserId(SessionContextHolder.getTenantId(), SessionContextHolder.getUserId());
            if(!CollectionUtils.isEmpty(subUserIds)){
                queryWrapper.in(WorkOrder::getExecutorId, subUserIds);
            }
        }

        // 关键字搜索
//        if (StringUtils.hasText(request.getKeyword())) {
//            queryWrapper.like(WorkOrder::getDescription, request.getKeyword());
//        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(WorkOrder::getPriority).orderByAsc(WorkOrder::getWorkOrderEndTime);

        return queryWrapper;
    }

    /**
     * 转换为列表项DTO
     */
    private List<WorkOrderListItemDTO> convertToListItemDTOs(WorkOrderQueryRequest request ,List<WorkOrder> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return new ArrayList<>();
        }

        // 收集所有用户ID
        Set<Long> userIds = new HashSet<>();
        Set<Long> excutorIds = new HashSet<>();
        for (WorkOrder workOrder : workOrders) {
            if (workOrder.getExecutorId() != null) {
                userIds.add(workOrder.getExecutorId());
            }
            if (workOrder.getCreatorId() != null) {
                userIds.add(workOrder.getCreatorId());
            }
            if(workOrder.getType() == ActionRelationEnums.CHECK_IN.getActionType()){
                excutorIds.add(workOrder.getExecutorId());
            }
        }

        // 批量查询用户信息
        //Map<Long, String> userNameMap = getUserNameMap(userIds);

        Map<Long, TenantUserInfoDTO> map = departmentRemoteService.queryUserInfoMapByUserIds(workOrders.get(0).getTenantId(), new ArrayList<>(userIds));

        Map<Long, CustomerInfo> customerInfoMap = new HashMap<>();
        Map<Long, String> areaNameMap = new HashMap<>();
        if(request.getType() == ActionRelationEnums.CHECK_IN.getActionType()){
            customerInfoMap =  customerMetricService.queryMapBySalesIds(workOrders.get(0).getTenantId(), new ArrayList<>(excutorIds));
            //获取customerInfoMap中所有的areaId
            Set<Long> areaIds = customerInfoMap.values().stream().map(CustomerInfo::getCustomerAreaId).collect(Collectors.toSet());

            areaNameMap = metricService.queryNames(workOrders.get(0).getTenantId(), MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), new HashSet<>(areaIds));

        }

        // 转换为DTO
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<WorkOrderListItemDTO> workOrderDTOs = new ArrayList<>();
        for(WorkOrder workOrder : workOrders){
            WorkOrderListItemDTO dto = new WorkOrderListItemDTO();
            dto.setId(workOrder.getId());
            dto.setDescription(workOrder.getDescription());
            dto.setExecutorId(workOrder.getExecutorId());

            //填充数据
            if(map.get(workOrder.getExecutorId()) != null){
                dto.setExecutorName(map.get(workOrder.getExecutorId()).getUnick());
                dto.setDeptName(map.get(workOrder.getExecutorId()).getDeptName());
            }

            if(map.get(workOrder.getCreatorId()) != null){
                dto.setCreatorName(map.get(workOrder.getCreatorId()).getUnick());
            }
            dto.setCreatorId(workOrder.getCreatorId());

            dto.setStatus(workOrder.getStatus());
            dto.setStatusDesc(getStatusDesc(workOrder.getStatus()));
            dto.setPriority(workOrder.getPriority());
            dto.setPriorityDesc(getPriorityDesc(workOrder.getPriority()));
            dto.setType(workOrder.getType());
            //只有拜访需要
            if(VisitingRecordEnum.map.get(workOrder.getSubType()) != null){
                dto.setSubTypeDesc(VisitingRecordEnum.map.get(workOrder.getSubType()).getActionDesc());
            }

            //设置大区名字
            if(request.getType() == ActionRelationEnums.CHECK_IN.getActionType()){
                if(customerInfoMap.get(workOrder.getExecutorId()) != null){
                   CustomerInfo customerInfo = customerInfoMap.get(workOrder.getExecutorId());
                   dto.setAreaName(areaNameMap.get(customerInfo.getCustomerAreaId()));
                }
            }

            dto.setCreated(workOrder.getCreated());
            if (workOrder.getCreated() != null) {
                dto.setCreatedStr(workOrder.getCreated().format(formatter));
            }
            workOrderDTOs.add(dto);
        }
        return workOrderDTOs;
    }

    /**
     * 批量获取用户名称
     */
    private Map<Long, String> getUserNameMap(Set<Long> userIds) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }

        try {
            // 调用用户服务批量查询
            SingleResponse<Map<Long, UserInfoDTO>> response = userService.queryMapByUserIds(new ArrayList<>(userIds));
            if (response.isSuccess() && response.getData() != null) {
                Map<Long, UserInfoDTO> userMap = response.getData();
                for (Map.Entry<Long, UserInfoDTO> entry : userMap.entrySet()) {
                    UserInfoDTO userInfo = entry.getValue();
                    String displayName = userInfo.getUnick() != null ? userInfo.getUnick() : userInfo.getUname();
                    result.put(entry.getKey(), displayName);
                }
            }
        } catch (Exception e) {
            log.error("批量查询用户名称失败", e);
        }

        return result;
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        WorkOrderStatusEnums statusEnum = WorkOrderStatusEnums.getByCode(status);
        return statusEnum != null ? statusEnum.getDesc() : "未知";
    }

    /**
     * 获取优先级描述
     */
    private String getPriorityDesc(Integer priority) {
        if (priority == null) {
            return "未知";
        }
        return WorkOrderPriorityEnums.getNameByCode(priority);
    }

    /**
     * 获取工单详情
     *
     * @param workOrderId 工单ID
     * @return 工单详情响应
     */
    @Override
    public SingleResponse<WorkOrderDetailResponse> getWorkOrderDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单ID不能为空");
            }

            // 查询工单基本信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "工单不存在");
            }

            // 构建响应对象
            WorkOrderDetailResponse response = new WorkOrderDetailResponse();

            // 1. 工单基本信息
            response.setWorkOrderInfo(buildWorkOrderBasicInfo(workOrder));

            // 2. 系统信息
            response.setSystemInfo(buildWorkOrderSystemInfo(workOrder));

            // 3. 工单进度
            response.setProgressList(buildWorkOrderProgress(workOrder));

            // 4. 工单备注
            response.setRemarkList(buildWorkOrderRemarks(workOrderId));

            // 5. 工单事件
            response.setEventList(buildWorkOrderEvents(workOrderId));

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询工单详情失败, workOrderId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询工单详情失败: " + e.getMessage());
        }
    }

    /**
     * 通过工单详情id查询工单详情
     *
     * @param workOrderDetailId
     */
    @Override
    public WorkOrderDetailDto getWorkOrderDetailById(Long workOrderDetailId) {
        WorkOrderDetail workOrderDetail = iworkOrderDetailRepository.getById(workOrderDetailId);
        return WorkOrderDetailDto.covertDto(workOrderDetail);
    }

    /**
     * 构建工单基本信息
     */
    private WorkOrderDetailResponse.WorkOrderBasicInfo buildWorkOrderBasicInfo(WorkOrder workOrder) {
        WorkOrderDetailResponse.WorkOrderBasicInfo basicInfo = new WorkOrderDetailResponse.WorkOrderBasicInfo();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        basicInfo.setId(workOrder.getId());
        basicInfo.setWorkOrderNo(workOrder.getId().toString()); // 使用ID作为工单编号
        basicInfo.setDescription(workOrder.getDescription());
        basicInfo.setCreated(workOrder.getCreated());
        if (workOrder.getCreated() != null) {
            basicInfo.setCreatedStr(workOrder.getCreated().format(formatter));
        }

        basicInfo.setExecutorId(workOrder.getExecutorId());
        basicInfo.setCreatorId(workOrder.getCreatorId());

        // 获取用户名称 todo 看看是否需要 @muzhao
//        Map<Long, String> userNameMap = getUserNameMap(Set.of(
//            workOrder.getExecutorId(), workOrder.getCreatorId()
//        ));
//        basicInfo.setExecutorName(userNameMap.get(workOrder.getExecutorId()));
//        basicInfo.setCreatorName(userNameMap.get(workOrder.getCreatorId()));

        // 设置时间信息
        basicInfo.setWorkOrderStartTime(workOrder.getWorkOrderStartTime());
        if (workOrder.getWorkOrderStartTime() != null) {
            basicInfo.setWorkOrderStartTimeStr(workOrder.getWorkOrderStartTime().format(formatter));
        }

        basicInfo.setWorkOrderEndTime(workOrder.getWorkOrderEndTime());
        if (workOrder.getWorkOrderEndTime() != null) {
            basicInfo.setWorkOrderEndTimeStr(workOrder.getWorkOrderEndTime().format(formatter));
        }

        // 根据状态设置完成时间
        if (WorkOrderStatusEnums.COMPLETED.getCode().equals(workOrder.getStatus())) {
            basicInfo.setCompletedTime(workOrder.getUpdated());
            if (workOrder.getUpdated() != null) {
                basicInfo.setCompletedTimeStr(workOrder.getUpdated().format(formatter));
            }
        }

        // 设置工单内容（从WorkOrderExtra中获取）
        basicInfo.setWorkContent(workOrder.getDescription());
        basicInfo.setAttachmentDesc("参考文件");

        return basicInfo;
    }

    /**
     * 构建系统信息
     */
    private WorkOrderDetailResponse.WorkOrderSystemInfo buildWorkOrderSystemInfo(WorkOrder workOrder) {
        WorkOrderDetailResponse.WorkOrderSystemInfo systemInfo = new WorkOrderDetailResponse.WorkOrderSystemInfo();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        systemInfo.setType(workOrder.getType());
        systemInfo.setStatus(workOrder.getStatus());
        systemInfo.setStatusDesc(getStatusDesc(workOrder.getStatus()));
        systemInfo.setPriority(workOrder.getPriority());
        systemInfo.setPriorityDesc(getPriorityDesc(workOrder.getPriority()));
        systemInfo.setCreated(workOrder.getCreated());
        systemInfo.setUpdated(workOrder.getUpdated());
        if (TimeUtils.getCurrentTime() > workOrder.getWorkOrderEndTime().atZone(ZoneId.of("UTC")).toEpochSecond()) {
            systemInfo.setIsDelay(true);
        }
        if (workOrder.getUpdated() != null) {
            systemInfo.setUpdatedStr(workOrder.getUpdated().format(formatter));
        }

        // 设置处理人信息
        systemInfo.setExecutorId(workOrder.getExecutorId());
        if (workOrder.getExecutorId() != null) {
            // 获取处理人详细信息
            SingleResponse<TenantUserInfoDTO> tenantUserInfoDTOSingleResponse = userTenantService.queryByUserId(workOrder.getTenantId(), workOrder.getExecutorId());
            if (tenantUserInfoDTOSingleResponse != null && tenantUserInfoDTOSingleResponse.isSuccess()) {
                systemInfo.setExecutorPhone(tenantUserInfoDTOSingleResponse.getData().getMobile());
                systemInfo.setDeptName(tenantUserInfoDTOSingleResponse.getData().getDeptName());
            }
        }

        return systemInfo;
    }

    /**
     * 构建工单进度
     */
    private List<WorkOrderDetailResponse.WorkOrderProgressItem> buildWorkOrderProgress(WorkOrder workOrder) {
        List<WorkOrderDetailResponse.WorkOrderProgressItem> progressList = new ArrayList<>();

        // 查询工单明细
        List<FlowConfig> flowConfigs=  ActionFlowConfig.actionFlowConfig.get(ActionRelationEnums.actionMap.get(workOrder.getType()));

        for(FlowConfig flowConfig:flowConfigs){
            WorkOrderDetailResponse.WorkOrderProgressItem tmp = new WorkOrderDetailResponse.WorkOrderProgressItem();
            tmp.setIsCurrent(flowConfig.getStatus() == workOrder.getStatus());
            tmp.setStepDesc(flowConfig.getAction());
            tmp.setTime(workOrder.getCreated());
            progressList.add( tmp);
        }

        return progressList;
    }

    /**
     * 构建工单备注
     */
    private List<WorkOrderRemarkInfo> buildWorkOrderRemarks(Long workOrderId) {
        List<WorkOrderRemarkInfo> remarkList = new ArrayList<>();

        try {
            // 查询工单备注
            WorkOrderRemarkVO queryVO = new WorkOrderRemarkVO();
            queryVO.setWorkOrderId(workOrderId);
            List<com.neo.nova.domain.dto.WorkOrderRemarkDTO> remarks = workOrderRemarksService.listWorkOrderRemark(queryVO);

            if (!CollectionUtils.isEmpty(remarks)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                // 获取所有用户ID
                Set<Long> userIds = new HashSet<>();
                for (com.neo.nova.domain.dto.WorkOrderRemarkDTO remark : remarks) {
                    if (remark.getCreatorId() != null) {
                        userIds.add(remark.getCreatorId());
                    }
                    if (remark.getRecipient() != null) {
                        userIds.add(remark.getRecipient());
                    }
                }

                Map<Long, String> userNameMap = getUserNameMap(userIds);

                for (com.neo.nova.domain.dto.WorkOrderRemarkDTO remark : remarks) {
                    WorkOrderRemarkInfo remarkInfo = new WorkOrderRemarkInfo();
                    remarkInfo.setId(remark.getId());
                    remarkInfo.setCreatorId(remark.getCreatorId());
                    remarkInfo.setCreatorName(userNameMap.get(remark.getCreatorId()));
                    remarkInfo.setRecipient(remark.getRecipient());
                    remarkInfo.setRecipientName(userNameMap.get(remark.getRecipient()));
                    remarkInfo.setContent(remark.getContent());
                    remarkInfo.setCreated(remark.getCreated());
                    if (remark.getCreated() != null) {
                        remarkInfo.setCreatedStr(remark.getCreated().format(formatter));
                    }
                    remarkInfo.setStatus(remark.getStatus());

                    remarkList.add(remarkInfo);
                }
            }
        } catch (Exception e) {
            log.error("查询工单备注失败, workOrderId: {}", workOrderId, e);
        }

        return remarkList;
    }

    /**
     * 构建工单事件
     */
    private List<WorkOrderDetailResponse.WorkOrderEventInfo> buildWorkOrderEvents(Long workOrderId) {
        List<WorkOrderDetailResponse.WorkOrderEventInfo> eventList = new ArrayList<>();

        try {
            // 查询工单事件
            LambdaQueryWrapper<WorkOrderEvent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderEvent::getWorkOrderId, workOrderId);
            queryWrapper.eq(WorkOrderEvent::getDeleted, 0);
            queryWrapper.orderByDesc(WorkOrderEvent::getCreated); // 按创建时间倒序

            List<WorkOrderEvent> events = iWorkOrderEventRepository.list(queryWrapper);

            if (!CollectionUtils.isEmpty(events)) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                // 获取所有操作人ID
                Set<Long> operatorIds = events.stream()
                        .map(WorkOrderEvent::getCreatorId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                Map<Long, String> userNameMap = getUserNameMap(operatorIds);

                for (WorkOrderEvent event : events) {
                    WorkOrderDetailResponse.WorkOrderEventInfo eventInfo = new WorkOrderDetailResponse.WorkOrderEventInfo();
                    eventInfo.setId(event.getId());
                    eventInfo.setEventType(getEventTypeDesc(event.getType()));
                    eventInfo.setEventDesc(event.getDescription());
                    eventInfo.setOperatorId(event.getCreatorId());
                    eventInfo.setOperatorName(userNameMap.get(event.getCreatorId()));
                    eventInfo.setEventTime(event.getCreated());
                    if (event.getCreated() != null) {
                        eventInfo.setEventTimeStr(event.getCreated().format(formatter));
                    }
                    eventInfo.setEventDetail(event.getDescription());

                    eventList.add(eventInfo);
                }
            }
        } catch (Exception e) {
            log.error("查询工单事件失败, workOrderId: {}", workOrderId, e);
        }

        return eventList;
    }


    /**
     * todo 转到enum维护
     * 获取事件类型描述
     */
    private String getEventTypeDesc(Integer eventType) {
        if (eventType == null || EventEnums.eventMap.get(eventType) == null) {
            return "未知事件";
        }

        return EventEnums.eventMap.get(eventType).getEventDesc();
    }

    /**
     * 获取拜访详情
     *
     * @param workOrderId 工单详情ID
     * @return 拜访详情响应
     */
    @Override
    public SingleResponse<SimpleDetailResponse> getVisitDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单详情ID不能为空");
            }

            // 查询工单详情
            LambdaQueryWrapper<WorkOrderDetail> lambdaQueryWrapper = new LambdaQueryWrapper ();
            lambdaQueryWrapper.eq(WorkOrderDetail::getWorkOrderId,workOrderId);
            WorkOrderDetail workOrderDetail = iworkOrderDetailRepository.getOne(lambdaQueryWrapper);
            if (workOrderDetail == null) {
                return SingleResponse.buildFailure("WORK_ORDER_DETAIL_NOT_FOUND", "工单详情不存在");
            }

            // 查询主工单信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderDetail.getWorkOrderId());
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "关联工单不存在");
            }

            // 构建响应对象
            SimpleDetailResponse response = buildVisitDetailResponse(workOrder, workOrderDetail);

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询拜访详情失败, workOrderDetailId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询拜访详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建拜访详情响应对象
     */
    private SimpleDetailResponse buildVisitDetailResponse(WorkOrder workOrder, WorkOrderDetail workOrderDetail) {
        SimpleDetailResponse response = new SimpleDetailResponse();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 解析拜访详情数据
        Map<String, Object> actionDetailMap = new HashMap<>();
        if (StringUtils.hasText(workOrderDetail.getActionDetail())) {
            try {
                actionDetailMap = JSON.parseObject(workOrderDetail.getActionDetail(), Map.class);
            } catch (Exception e) {
                log.warn("解析拜访详情数据失败: {}", workOrderDetail.getActionDetail(), e);
            }
        }

        // 1. 构建基本信息
        SimpleDetailResponse.BasicInfo visitInfo = new SimpleDetailResponse.BasicInfo();
        visitInfo.setWorkOrderDetailId(workOrderDetail.getId());
        visitInfo.setTitle(workOrder.getTitle());
        visitInfo.setExecutorId(workOrderDetail.getExecutorId());

        visitInfo.setSubTypeDesc(ActionSubTypeEnums.map.get(workOrderDetail.getType()));

        // 获取执行人姓名
        if (workOrderDetail.getExecutorId() != null) {
            try {
                // 这里可以调用用户服务获取用户信息
                TenantUserInfoDTO tenantUserInfoDTO = departmentRemoteService.queryUserInfoByUserId(workOrderDetail.getTenantId(),workOrderDetail.getExecutorId());
                if(tenantUserInfoDTO != null){
                    visitInfo.setExecutorName(tenantUserInfoDTO.getUnick());
                    visitInfo.setDeptName(tenantUserInfoDTO.getDeptName());

                }
            } catch (Exception e) {
                log.warn("获取执行人信息失败: {}", workOrderDetail.getExecutorId(), e);
                visitInfo.setExecutorName("未知");
            }
        }

        visitInfo.setCreated(workOrderDetail.getCreated());
        if (workOrderDetail.getCreated() != null) {
            visitInfo.setCreatedStr(workOrderDetail.getCreated().format(formatter));
        }
        visitInfo.setCompletedTime(workOrderDetail.getCompleteTime());
        if (workOrderDetail.getCompleteTime() != null) {
            visitInfo.setCompletedTimeStr(workOrderDetail.getCompleteTime().format(formatter));
        }

        response.setVisitInfo(visitInfo);

        //工单信息
        response.setWorkOrderRemarkInfos(buildWorkOrderRemarks(workOrderDetail.getWorkOrderId()));

        //详细的拜访信息
        response.setVisitExtraInfo(actionDetailMap);

        return response;
    }

    /**
     * 获取导购日常打卡详情
     *
     * @param workOrderId 工单ID
     * @return 打卡详情响应
     */
    @Override
    public SingleResponse<CheckInDetailResponse> getCheckInDetail(Long workOrderId) {
        try {
            if (workOrderId == null) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单ID不能为空");
            }

            // 查询工单基本信息
            WorkOrder workOrder = iWorkOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                return SingleResponse.buildFailure("WORK_ORDER_NOT_FOUND", "工单不存在");
            }

            // 查询工单详情列表
            List<WorkOrderDetailDto> workOrderDetails = getWorkOrderDetailByWorkOrderId(workOrderId);
            if (CollectionUtils.isEmpty(workOrderDetails)) {
                return SingleResponse.buildFailure("WORK_ORDER_DETAILS_NOT_FOUND", "工单详情不存在");
            }

            // 构建响应对象
            CheckInDetailResponse response = buildCheckInDetailResponse(workOrder, workOrderDetails);

            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("查询导购日常打卡详情失败, workOrderId: {}", workOrderId, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "查询导购日常打卡详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建导购日常打卡详情响应对象
     */
    private CheckInDetailResponse buildCheckInDetailResponse(WorkOrder workOrder, List<WorkOrderDetailDto> workOrderDetails) {
        CheckInDetailResponse response = new CheckInDetailResponse();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 2. 构建打卡任务列表
        List<CheckInDetailResponse.CheckInTaskItem> taskList = new ArrayList<>();
        for (WorkOrderDetailDto detail : workOrderDetails) {
            CheckInDetailResponse.CheckInTaskItem taskItem = buildCheckInTaskItem(detail, formatter);
            taskList.add(taskItem);
        }
        response.setTaskList(taskList);

        return response;
    }

    /**
     * 构建打卡任务项
     */
    private CheckInDetailResponse.CheckInTaskItem buildCheckInTaskItem(WorkOrderDetailDto detail, DateTimeFormatter formatter) {
        CheckInDetailResponse.CheckInTaskItem taskItem = new CheckInDetailResponse.CheckInTaskItem();

        taskItem.setWorkOrderDetailId(detail.getId());
        taskItem.setSort(detail.getSort());
        taskItem.setType(detail.getType());
        taskItem.setStatus(detail.getStatus());
        taskItem.setStatusDesc(WorkOrderDetailStatusEnums.MAP.get(detail.getStatus()).getDesc());
        taskItem.setCompleteTime(detail.getCompleteTime());
        if (detail.getCompleteTime() != null) {
            taskItem.setCompleteTimeStr(detail.getCompleteTime().format(formatter));
        }

        // 设置任务类型描述
        taskItem.setTypeDesc(getTaskTypeDesc(detail.getAction(), detail.getType()));

        // 解析任务详情数据
        if(StringUtils.hasText(detail.getActionDetail())){
            taskItem.setTaskDetail(JSONObject.parseObject(detail.getActionDetail(),Map.class));
        }

        return taskItem;
    }



    /**
     * 获取任务类型描述
     */
    private String getTaskTypeDesc(String action, Integer type) {
        if (type == null) {
            return "未知任务";
        }

        Map<Integer, ActionSubTypeEnums> actionTypeMap = ActionSubTypeEnums.getByActionTypeAndType(action);
        ActionSubTypeEnums actionSubType = actionTypeMap.get(type);

        return actionSubType != null ? actionSubType.getActionDesc() : "未知任务";
    }

    /**
     * 获取工单类型列表
     *
     * @return 工单类型响应
     */
    @Override
    public SingleResponse<WorkOrderTypeResponse> getWorkOrderTypes() {
        try {
            WorkOrderTypeResponse response = new WorkOrderTypeResponse();
            List<ChooseOption> workOrderTypes = new ArrayList<>();

            // 遍历所有的 ActionRelationEnums 枚举值
            for (ActionRelationEnums actionEnum : ActionRelationEnums.values()) {
                ChooseOption item = new ChooseOption(actionEnum.getActionDesc(), String.valueOf(actionEnum.getActionType()));
                workOrderTypes.add(item);
            }

            response.setWorkOrderTypes(workOrderTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取工单类型列表失败", e);
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据工单类型获取子类型列表
     *
     * @param actionName 工单类型名称
     * @return 工单子类型响应
     */
    @Override
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderSubTypes(String actionName) {
        try {
            if (!StringUtils.hasText(actionName)) {
                return SingleResponse.buildFailure("INVALID_PARAM", "工单类型名称不能为空");
            }

            ActionRelationEnums actionRelationEnum = ActionRelationEnums.actionMap.get(Integer.parseInt(actionName));

            if(actionRelationEnum == null){
                return SingleResponse.buildFailure("INVALID_PARAM", "工单类型名称不能为空");
            }
            // 根据actionName获取对应的子类型
            Map<Integer, ActionSubTypeEnums> subTypeMap = ActionSubTypeEnums.getByActionTypeAndType(actionRelationEnum.getActionName());

            if (subTypeMap.isEmpty()) {
                return SingleResponse.buildFailure("NO_SUB_TYPES", "该工单类型没有子类型");
            }

            WorkOrderSubTypeResponse response = new WorkOrderSubTypeResponse();
            response.setActionName(actionName);

            // 设置主类型描述
            ActionRelationEnums actionRelation = getActionRelationByName(actionName);
            if (actionRelation != null) {
                response.setActionDesc(actionRelation.getActionDesc());
            }

            // 构建子类型列表
            List<ChooseOption> subTypes = new ArrayList<>();
            for (Map.Entry<Integer, ActionSubTypeEnums> entry : subTypeMap.entrySet()) {
                ActionSubTypeEnums subTypeEnum = entry.getValue();
                ChooseOption item = new ChooseOption(subTypeEnum.getActionDesc(), String.valueOf(subTypeEnum.getType()));
                subTypes.add(item);
            }

            // 按key排序
            //subTypes.sort((a, b) -> a.getKey().compareTo(b.getKey()));

            response.setSubTypes(subTypes);
            return SingleResponse.buildSuccess(response);

        } catch (Exception e) {
            log.error("获取工单子类型列表失败, actionName: {}", actionName, e);
            return SingleResponse.buildFailure("QUERY_ERROR", "获取工单子类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据actionName获取ActionRelationEnums
     */
    private ActionRelationEnums getActionRelationByName(String actionName) {
        for (ActionRelationEnums actionEnum : ActionRelationEnums.values()) {
            if (actionEnum.getActionName().equals(actionName)) {
                return actionEnum;
            }
        }
        return null;
    }

    @Override
    @Transactional
    public SingleResponse<Boolean> updateWorkOrderAndDetail(ActionTaskModel actionTaskModel) {
        log.info("updateWorkOrderAndDetail: {}", actionTaskModel);
        try {

            WorkOrder workOrder = convertToWorkORder(actionTaskModel);
            iWorkOrderRepository.updateById(workOrder);

            //可能的更新
            LambdaUpdateWrapper<WorkOrderDetail> updateWrapper = new LambdaUpdateWrapper();
            updateWrapper.eq(WorkOrderDetail::getWorkOrderId, actionTaskModel.getWorkOrderId());
            updateWrapper.set(WorkOrderDetail::getAction, ActionRelationEnums.actionMap.get(actionTaskModel.getActionType()).getActionName());
            updateWrapper.set(WorkOrderDetail::getType, actionTaskModel.getWorkOrderDetailType());
            updateWrapper.set(WorkOrderDetail::getBizId, actionTaskModel.getCustomerId());
            updateWrapper.set(WorkOrderDetail::getBizId, actionTaskModel.getCustomerId());
            updateWrapper.set(WorkOrderDetail::getExecutorId, actionTaskModel.getUserId());
            updateWrapper.set(WorkOrderDetail::getActionDetail, JSON.toJSONString(actionTaskModel.getActionDetail()));
            iworkOrderDetailRepository.update(updateWrapper);


        } catch (Exception e) {
            log.error("更新工单和工单详情失败", e);
            return SingleResponse.buildFailure("UPDATE_ERROR", "更新工单和工单详情失败: " + e.getMessage());
        }
        return SingleResponse.buildSuccess(true);
    }

    public static WorkOrder convertToWorkORder(ActionTaskModel actionTaskModel) {
        WorkOrder workOrder = new WorkOrder();
        //todo 选择创建人
        workOrder.setCreatorId(SessionContextHolder.getUserId());
        workOrder.setExecutorId(actionTaskModel.getUserId());
        workOrder.setUpdaterId(1L);
        workOrder.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        workOrder.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
        workOrder.setPriority(actionTaskModel.getPriority());
        workOrder.setTenantId(actionTaskModel.getTenantId());
        workOrder.setType(actionTaskModel.getActionType());
        workOrder.setId(actionTaskModel.getWorkOrderId());
        workOrder.setSubType(actionTaskModel.getWorkOrderDetailType());
        return workOrder;
    }
}
