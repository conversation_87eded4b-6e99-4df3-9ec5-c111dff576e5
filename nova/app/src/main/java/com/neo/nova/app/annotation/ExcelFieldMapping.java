package com.neo.nova.app.annotation;

import com.neo.nova.app.enums.ExcelDataOriginalField;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel字段映射注解
 * 用于标记Excel实体字段与数据库实体字段的映射关系
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelFieldMapping {

    /**
     * 映射到ExcelDataOriginal实体的字段（支持多字段映射）
     * @return ExcelDataOriginal字段枚举数组
     */
    ExcelDataOriginalField[] fields() default {};

    /**
     * 源日期格式（用于日期格式转换）
     * @return 源日期格式字符串
     */
    String sourceDateFormat() default "";
}
