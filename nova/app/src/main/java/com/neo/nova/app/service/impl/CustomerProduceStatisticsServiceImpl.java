package com.neo.nova.app.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.service.CustomerProduceStatisticsService;
import com.neo.nova.domain.constants.DataConstants;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.entity.st_trd_customer_produce_month_info;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_month_infoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.neo.nova.domain.constants.DataConstants.SUMMARY_GOODS_ID;
import static com.neo.nova.domain.constants.DataConstants.SUMMARY_GOODS_NAME;

/**
 * 客户产品统计数据服务实现
 * 负责处理 st_trd_customer_produce_day_info 和 st_trd_customer_produce_month_info 表的新增/修改操作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class CustomerProduceStatisticsServiceImpl implements CustomerProduceStatisticsService {

    @Autowired
    private st_trd_customer_produce_day_infoRepository dayInfoRepository;

    @Autowired
    private st_trd_customer_produce_month_infoRepository monthInfoRepository;

    /**
     * 数据库批量操作专用线程池
     */
    private final ExecutorService batchOperationExecutor = new ThreadPoolExecutor(
            8,  // 核心线程数
            16, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(100), // 任务队列
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "BatchOperation-" + counter.incrementAndGet());
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
    );

    @Override
    public void saveDayInfoRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {
        if (dayInfoList == null || dayInfoList.isEmpty()) {
            log.debug("日统计数据列表为空，跳过保存");
            return;
        }

        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> inputMap = Maps.newHashMap();
        for (st_trd_customer_produce_day_info inputDayInfo : dayInfoList) {
            inputMap.computeIfAbsent(inputDayInfo.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(inputDayInfo.getCustomerId(), k -> new HashMap<>())
                    .put(inputDayInfo.getProduceId(), inputDayInfo);
        }

        List<st_trd_customer_produce_day_info> recordsToInsert = new ArrayList<>();
        List<st_trd_customer_produce_day_info> recordsToUpdate = new ArrayList<>();

        // 批量查询所有可能存在的日统计记录
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> existingDayRecordsMap = batchQueryExistingDayRecords(dayInfoList, tenantId);

        for (Map.Entry<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> dataEntry : inputMap.entrySet()) {
            String visitDate = dataEntry.getKey();
            Map<Long, Map<Long, st_trd_customer_produce_day_info>> customerRecordMap = existingDayRecordsMap.getOrDefault(visitDate, Maps.newHashMap());
            for (Map.Entry<Long, Map<Long, st_trd_customer_produce_day_info>> customerEntry : dataEntry.getValue().entrySet()) {
                Long customerId = customerEntry.getKey();
                Map<Long, st_trd_customer_produce_day_info> goodsMap = customerEntry.getValue();
                Map<Long, st_trd_customer_produce_day_info> goodsRecordMap = customerRecordMap.getOrDefault(customerId, Maps.newHashMap());
                //如果传入了合计值，以传入为准
                boolean processedSummary = false;
                if (goodsMap.containsKey(SUMMARY_GOODS_ID)) {
                    insertOrUpdate(goodsMap.get(SUMMARY_GOODS_ID), goodsRecordMap.get(SUMMARY_GOODS_ID), recordsToInsert, recordsToUpdate);
                    processedSummary = true;
                }
                //处理单个数据
                for (Map.Entry<Long, st_trd_customer_produce_day_info> goodsEntry : goodsMap.entrySet()) {
                    Long goodsId = goodsEntry.getKey();
                    st_trd_customer_produce_day_info input = goodsEntry.getValue();
                    insertOrUpdate(input, goodsRecordMap.get(goodsId), recordsToInsert, recordsToUpdate);
                    //循环后goodsRecordMap数据会被写入覆盖
                    goodsRecordMap.put(goodsId, input);
                }
                //处理db里的合计值
                if (!processedSummary) {
                    //合计值
                    st_trd_customer_produce_day_info recordSummary = goodsRecordMap.get(SUMMARY_GOODS_ID);
                    goodsRecordMap.remove(SUMMARY_GOODS_ID);
                    //存在其他记录时更新
                    if (!goodsRecordMap.isEmpty()) {
                        st_trd_customer_produce_day_info newSummary = createDaySummary(goodsRecordMap.values());
                        enableUpdateSummary(newSummary, recordSummary, recordsToInsert, recordsToUpdate);
                    }
                }

            }
        }

        // 使用多线程批量处理记录
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 批量插入新记录
        if (!recordsToInsert.isEmpty()) {
            log.info("开始多线程新增日统计数据，批量插入新记录：{}条", recordsToInsert.size());
            CompletableFuture<Void> insertFuture = batchInsertDayRecordsAsync(recordsToInsert);
            futures.add(insertFuture);
        }

        // 批量更新现有记录
        if (!recordsToUpdate.isEmpty()) {
            log.info("开始多线程更新日统计数据，批量更新记录：{}条", recordsToUpdate.size());
            CompletableFuture<Void> updateFuture = batchUpdateDayRecordsAsync(recordsToUpdate);
            futures.add(updateFuture);
        }

        // 等待所有任务完成
        if (!futures.isEmpty()) {
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("日统计数据多线程处理异常", e);
                throw new RuntimeException("数据库批量操作失败", e);
            }
        }

        log.info("日统计数据多线程处理完成，共处理：新增{}条，更新{}条",
                recordsToInsert.size(), recordsToUpdate.size());
        // 日表数据保存完成后，汇总到月表
        if (!dayInfoList.isEmpty()) {
            log.info("开始汇总日表数据到月表，日表记录数：{}", dayInfoList.size());
            aggregateDayToMonthRecords(dayInfoList, tenantId);
        }
    }

    private void insertOrUpdate(st_trd_customer_produce_day_info input, st_trd_customer_produce_day_info exist,
                                List<st_trd_customer_produce_day_info> recordsToInsert,
                                List<st_trd_customer_produce_day_info> recordsToUpdate) {
        if (exist == null) {
            // 创建新记录
            input.setUpdated(TimeUtils.getCurrentTime()); // 设置更新时间
            recordsToInsert.add(input);
        } else {
            // 数据比对：如果数据库里的数据和要更新的数据没有区别，那就不更新
            if (!isDayRecordDataEqual(exist, input)) {
                // 更新现有记录
                input.setId(exist.getId());
                input.setCreated(null); // 保持原创建时间
                recordsToUpdate.add(input);
            }
        }
    }

    private void insertOrUpdate(st_trd_customer_produce_month_info input, st_trd_customer_produce_month_info exist,
                                List<st_trd_customer_produce_month_info> recordsToInsert,
                                List<st_trd_customer_produce_month_info> recordsToUpdate) {
        if (exist == null) {
            // 创建新记录
            input.setUpdated(TimeUtils.getCurrentTime()); // 设置更新时间
            recordsToInsert.add(input);
        } else {
            // 数据比对：如果数据库里的数据和要更新的数据没有区别，那就不更新
            if (!isMonthRecordDataEqual(exist, input)) {
                // 更新现有记录
                input.setId(exist.getId());
                input.setCreated(null); // 保持原创建时间
                recordsToUpdate.add(input);
            }
        }
    }

    @Override
    public void saveMonthInfoRecords(List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId, boolean fromDay) {
        if (monthInfoList == null || monthInfoList.isEmpty()) {
            log.debug("月统计数据列表为空，跳过保存");
            return;
        }

        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> inputMap = Maps.newHashMap();
        for (st_trd_customer_produce_month_info inputMonthInfo : monthInfoList) {
            inputMap.computeIfAbsent(inputMonthInfo.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(inputMonthInfo.getCustomerId(), k -> new HashMap<>())
                    .put(inputMonthInfo.getProduceId(), inputMonthInfo);
        }

        List<st_trd_customer_produce_month_info> recordsToInsert = new ArrayList<>();
        List<st_trd_customer_produce_month_info> recordsToUpdate = new ArrayList<>();

        // 批量查询所有可能存在的日统计记录
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> existingMonthRecordsMap = batchQueryExistingMonthRecords(monthInfoList, tenantId);

        for (Map.Entry<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> dataEntry : inputMap.entrySet()) {
            String visitDate = dataEntry.getKey();
            Map<Long, Map<Long, st_trd_customer_produce_month_info>> customerRecordMap = existingMonthRecordsMap.getOrDefault(visitDate, Maps.newHashMap());
            for (Map.Entry<Long, Map<Long, st_trd_customer_produce_month_info>> customerEntry : dataEntry.getValue().entrySet()) {
                Long customerId = customerEntry.getKey();
                Map<Long, st_trd_customer_produce_month_info> goodsMap = customerEntry.getValue();
                Map<Long, st_trd_customer_produce_month_info> goodsRecordMap = customerRecordMap.getOrDefault(customerId, Maps.newHashMap());
                //如果传入了合计值，以传入为准
                boolean processedSummary = false;
                if (goodsMap.containsKey(SUMMARY_GOODS_ID)) {
                    //由日表上报的合计值，需要和数据库比较、防止用户指定合计值的情况
                    if (fromDay) {
                        st_trd_customer_produce_month_info recordSummary = goodsRecordMap.get(SUMMARY_GOODS_ID);
                        enableUpdateSummary(goodsMap.get(SUMMARY_GOODS_ID), recordSummary, recordsToInsert, recordsToUpdate);
                    } else {
                        insertOrUpdate(goodsMap.get(SUMMARY_GOODS_ID), goodsRecordMap.get(SUMMARY_GOODS_ID), recordsToInsert, recordsToUpdate);
                    }
                    processedSummary = true;
                }
                //处理单个数据
                for (Map.Entry<Long, st_trd_customer_produce_month_info> goodsEntry : goodsMap.entrySet()) {
                    Long goodsId = goodsEntry.getKey();
                    st_trd_customer_produce_month_info input = goodsEntry.getValue();
                    insertOrUpdate(input, goodsRecordMap.get(goodsId), recordsToInsert, recordsToUpdate);
                    //循环后goodsRecordMap数据会被写入覆盖
                    goodsRecordMap.put(goodsId, input);
                }
                //处理db里的合计值
                if (!processedSummary) {
                    //合计值
                    st_trd_customer_produce_month_info recordSummary = goodsRecordMap.get(SUMMARY_GOODS_ID);
                    goodsRecordMap.remove(SUMMARY_GOODS_ID);
                    //存在其他记录时更新
                    if (!goodsRecordMap.isEmpty()) {
                        st_trd_customer_produce_month_info newSummary = createMonthSummary(goodsRecordMap.values());
                        enableUpdateSummary(newSummary, recordSummary, recordsToInsert, recordsToUpdate);
                    }
                }

            }
        }

        // 使用多线程批量处理记录
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 批量插入新记录
        if (!recordsToInsert.isEmpty()) {
            log.info("开始多线程新增月统计数据，批量插入新记录：{}条", recordsToInsert.size());
            CompletableFuture<Void> insertFuture = batchInsertMonthRecordsAsync(recordsToInsert);
            futures.add(insertFuture);
        }

        // 批量更新现有记录
        if (!recordsToUpdate.isEmpty()) {
            log.info("开始多线程更新月统计数据，批量更新记录：{}条", recordsToUpdate.size());
            CompletableFuture<Void> updateFuture = batchUpdateMonthRecordsAsync(recordsToUpdate);
            futures.add(updateFuture);
        }

        // 等待所有任务完成
        if (!futures.isEmpty()) {
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("月统计数据多线程处理异常", e);
                throw new RuntimeException("数据库批量操作失败", e);
            }
        }

        log.info("月统计数据多线程处理完成，共处理：新增{}条，更新{}条",
                recordsToInsert.size(), recordsToUpdate.size());
    }

    /**
     * 批量查询已存在的日统计记录
     */
    private Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> batchQueryExistingDayRecords(
            List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {

        if (dayInfoList.isEmpty()) {
            return new HashMap<>();
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_day_info data : dayInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.add(data.getVisitDate());
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_day_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_day_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_day_info::getIsDeleted, 0);

        List<st_trd_customer_produce_day_info> existingRecords = dayInfoRepository.list(batchQuery);


        // 构建查询结果映射
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_day_info>>> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_day_info record : existingRecords) {
            recordsMap.computeIfAbsent(record.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(record.getCustomerId(), k -> new HashMap<>())
                    .put(record.getProduceId(), record);
        }

        log.info("批量查询日统计记录完成，查询到 {} 条已存在记录", existingRecords.size());
        return recordsMap;
    }

    /**
     * 批量查询已存在的月统计记录
     */
    private Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> batchQueryExistingMonthRecords(
            List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId) {

        if (monthInfoList.isEmpty()) {
            return new HashMap<>();
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_month_info data : monthInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.add(data.getVisitDate());
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_month_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_month_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_month_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_month_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_month_info::getIsDeleted, 0);

        List<st_trd_customer_produce_month_info> existingRecords = monthInfoRepository.list(batchQuery);

        // 构建查询结果映射
        Map<String, Map<Long, Map<Long, st_trd_customer_produce_month_info>>> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_month_info record : existingRecords) {
            recordsMap.computeIfAbsent(record.getVisitDate(), k -> new HashMap<>())
                    .computeIfAbsent(record.getCustomerId(), k -> new HashMap<>())
                    .put(record.getProduceId(), record);
        }

        log.info("批量查询月统计记录完成，查询到 {} 条已存在记录", existingRecords.size());
        return recordsMap;
    }


    /**
     * 比较两个日统计记录的数据是否相等
     *
     * @param existing  数据库中的现有记录
     * @param newRecord 要更新的新记录
     * @return true表示数据相等，false表示数据不同
     */
    private boolean isDayRecordDataEqual(st_trd_customer_produce_day_info existing, st_trd_customer_produce_day_info newRecord) {
        if (existing == null || newRecord == null) {
            return false;
        }

        // 比较核心业务字段
        return (newRecord.getDelivAmount() == null || Objects.equals(existing.getDelivAmount(), newRecord.getDelivAmount())) &&
                (newRecord.getAmount() == null || Objects.equals(existing.getAmount(), newRecord.getAmount())) &&
                (newRecord.getSalesQty() == null || Objects.equals(existing.getSalesQty(), newRecord.getSalesQty())) &&
                (newRecord.getDelivyQty() == null || Objects.equals(existing.getDelivyQty(), newRecord.getDelivyQty())) &&
                (newRecord.getBillMoney() == null || Objects.equals(existing.getBillMoney(), newRecord.getBillMoney())) &&
                (newRecord.getCostAmount() == null || Objects.equals(existing.getCostAmount(), newRecord.getCostAmount())) &&
                (newRecord.getProduceName() == null || Objects.equals(existing.getProduceName(), newRecord.getProduceName())) &&
                (newRecord.getProduceCode() == null || Objects.equals(existing.getProduceCode(), newRecord.getProduceCode())) &&
                (newRecord.getProduceTypeId() == null || Objects.equals(existing.getProduceTypeId(), newRecord.getProduceTypeId())) &&
                (newRecord.getCustomerName() == null || Objects.equals(existing.getCustomerName(), newRecord.getCustomerName())) &&
                (newRecord.getCustomerCode() == null || Objects.equals(existing.getCustomerCode(), newRecord.getCustomerCode())) &&
                (newRecord.getCustomerMnemoCode() == null || Objects.equals(existing.getCustomerMnemoCode(), newRecord.getCustomerMnemoCode())) &&
                (newRecord.getCustomerTypeId() == null || Objects.equals(existing.getCustomerTypeId(), newRecord.getCustomerTypeId())) &&
                (newRecord.getCustomerLevel() == null || Objects.equals(existing.getCustomerLevel(), newRecord.getCustomerLevel())) &&
                (newRecord.getSalesId() == null || Objects.equals(existing.getSalesId(), newRecord.getSalesId())) &&
                (newRecord.getSalesCode() == null || Objects.equals(existing.getSalesCode(), newRecord.getSalesCode())) &&
                (newRecord.getSalesName() == null || Objects.equals(existing.getSalesName(), newRecord.getSalesName())) &&
                (newRecord.getSalesRegionId() == null || Objects.equals(existing.getSalesRegionId(), newRecord.getSalesRegionId())) &&
                (newRecord.getAdminRegionId() == null || Objects.equals(existing.getAdminRegionId(), newRecord.getAdminRegionId())) &&
                (newRecord.getChannel() == null || Objects.equals(existing.getChannel(), newRecord.getChannel())) &&
                (newRecord.getSupermarketAreaId() == null || Objects.equals(existing.getSupermarketAreaId(), newRecord.getSupermarketAreaId()));
    }

    /**
     * 比较两个月统计记录的数据是否相等
     *
     * @param existing  数据库中的现有记录
     * @param newRecord 要更新的新记录
     * @return true表示数据相等，false表示数据不同
     */
    private boolean isMonthRecordDataEqual(st_trd_customer_produce_month_info existing, st_trd_customer_produce_month_info newRecord) {
        if (existing == null || newRecord == null) {
            return false;
        }

        // 比较核心业务字段
        return (newRecord.getDelivAmount() == null || Objects.equals(existing.getDelivAmount(), newRecord.getDelivAmount())) &&
                (newRecord.getAmount() == null || Objects.equals(existing.getAmount(), newRecord.getAmount())) &&
                (newRecord.getSalesQty() == null || Objects.equals(existing.getSalesQty(), newRecord.getSalesQty())) &&
                (newRecord.getDelivyQty() == null || Objects.equals(existing.getDelivyQty(), newRecord.getDelivyQty())) &&
                (newRecord.getBillMoney() == null || Objects.equals(existing.getBillMoney(), newRecord.getBillMoney())) &&
                (newRecord.getCostAmount() == null || Objects.equals(existing.getCostAmount(), newRecord.getCostAmount())) &&
                (newRecord.getProduceName() == null || Objects.equals(existing.getProduceName(), newRecord.getProduceName())) &&
                (newRecord.getProduceCode() == null || Objects.equals(existing.getProduceCode(), newRecord.getProduceCode())) &&
                (newRecord.getProduceTypeId() == null || Objects.equals(existing.getProduceTypeId(), newRecord.getProduceTypeId())) &&
                (newRecord.getCustomerName() == null || Objects.equals(existing.getCustomerName(), newRecord.getCustomerName())) &&
                (newRecord.getCustomerCode() == null || Objects.equals(existing.getCustomerCode(), newRecord.getCustomerCode())) &&
                (newRecord.getCustomerMnemoCode() == null || Objects.equals(existing.getCustomerMnemoCode(), newRecord.getCustomerMnemoCode())) &&
                (newRecord.getCustomerTypeId() == null || Objects.equals(existing.getCustomerTypeId(), newRecord.getCustomerTypeId())) &&
                (newRecord.getCustomerLevel() == null || Objects.equals(existing.getCustomerLevel(), newRecord.getCustomerLevel())) &&
                (newRecord.getSalesId() == null || Objects.equals(existing.getSalesId(), newRecord.getSalesId())) &&
                (newRecord.getSalesCode() == null || Objects.equals(existing.getSalesCode(), newRecord.getSalesCode())) &&
                (newRecord.getSalesName() == null || Objects.equals(existing.getSalesName(), newRecord.getSalesName())) &&
                (newRecord.getSalesRegionId() == null || Objects.equals(existing.getSalesRegionId(), newRecord.getSalesRegionId())) &&
                (newRecord.getAdminRegionId() == null || Objects.equals(existing.getAdminRegionId(), newRecord.getAdminRegionId())) &&
                (newRecord.getChannel() == null || Objects.equals(existing.getChannel(), newRecord.getChannel())) &&
                (newRecord.getSupermarketAreaId() == null || Objects.equals(existing.getSupermarketAreaId(), newRecord.getSupermarketAreaId()));
    }

    /**
     * 多线程批量插入日统计记录
     */
    private CompletableFuture<Void> batchInsertDayRecordsAsync(List<st_trd_customer_produce_day_info> records) {
        return CompletableFuture.runAsync(() -> {
            batchProcessRecords(records, "插入", (batch, batchIndex, totalBatches) -> {
                dayInfoRepository.saveBatch(batch);
                log.info("日统计数据插入进度：第{}/{}组完成，本组{}条记录",
                        batchIndex, totalBatches, batch.size());
            });
        }, batchOperationExecutor);
    }

    /**
     * 多线程批量更新日统计记录
     */
    private CompletableFuture<Void> batchUpdateDayRecordsAsync(List<st_trd_customer_produce_day_info> records) {
        return CompletableFuture.runAsync(() -> {
            batchProcessRecords(records, "更新", (batch, batchIndex, totalBatches) -> {
                dayInfoRepository.updateBatchById(batch);
                log.info("日统计数据更新进度：第{}/{}组完成，本组{}条记录",
                        batchIndex, totalBatches, batch.size());
            });
        }, batchOperationExecutor);
    }

    /**
     * 多线程批量插入月统计记录
     */
    private CompletableFuture<Void> batchInsertMonthRecordsAsync(List<st_trd_customer_produce_month_info> records) {
        return CompletableFuture.runAsync(() -> {
            batchProcessRecords(records, "插入", (batch, batchIndex, totalBatches) -> {
                monthInfoRepository.saveBatch(batch);
                log.info("月统计数据插入进度：第{}/{}组完成，本组{}条记录",
                        batchIndex, totalBatches, batch.size());
            });
        }, batchOperationExecutor);
    }

    /**
     * 多线程批量更新月统计记录
     */
    private CompletableFuture<Void> batchUpdateMonthRecordsAsync(List<st_trd_customer_produce_month_info> records) {
        return CompletableFuture.runAsync(() -> {
            batchProcessRecords(records, "更新", (batch, batchIndex, totalBatches) -> {
                monthInfoRepository.updateBatchById(batch);
                log.info("月统计数据更新进度：第{}/{}组完成，本组{}条记录",
                        batchIndex, totalBatches, batch.size());
            });
        }, batchOperationExecutor);
    }

    /**
     * 通用的批量处理方法
     *
     * @param records       要处理的记录列表
     * @param operationType 操作类型（用于日志）
     * @param processor     具体的处理逻辑
     */
    private <T> void batchProcessRecords(List<T> records, String operationType, BatchProcessor<T> processor) {
        if (records == null || records.isEmpty()) {
            return;
        }

        final int batchSize = 500; // 每组500条记录
        final int totalRecords = records.size();
        final int totalBatches = (totalRecords + batchSize - 1) / batchSize;

        log.info("开始{}操作，总记录数：{}，分{}组处理，每组{}条",
                operationType, totalRecords, totalBatches, batchSize);

        List<CompletableFuture<Void>> batchFutures = new ArrayList<>();

        for (int i = 0; i < totalRecords; i += batchSize) {
            final int startIndex = i;
            final int endIndex = Math.min(i + batchSize, totalRecords);
            final int batchIndex = (i / batchSize) + 1;
            final List<T> batch = records.subList(startIndex, endIndex);

            CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() -> {
                try {
                    processor.process(batch, batchIndex, totalBatches);
                } catch (Exception e) {
                    log.error("{}操作第{}/{}组失败，记录数：{}",
                            operationType, batchIndex, totalBatches, batch.size(), e);
                    throw new RuntimeException("批量" + operationType + "操作失败", e);
                }
            }, batchOperationExecutor);

            batchFutures.add(batchFuture);
        }

        // 等待所有批次完成
        try {
            CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0])).get();
            log.info("{}操作全部完成，共处理{}条记录，分{}组", operationType, totalRecords, totalBatches);
        } catch (InterruptedException | ExecutionException e) {
            log.error("{}操作等待完成时发生异常", operationType, e);
            throw new RuntimeException("批量" + operationType + "操作等待完成失败", e);
        }
    }

    private st_trd_customer_produce_day_info createDaySummary(Collection<st_trd_customer_produce_day_info> allRecords) {
        st_trd_customer_produce_day_info summary = new st_trd_customer_produce_day_info();
        st_trd_customer_produce_day_info anyOne = allRecords.stream().findAny().get();
        BeanUtils.copyProperties(anyOne, summary);
        summary.setId(null);
        summary.setProduceId(SUMMARY_GOODS_ID);
        summary.setProduceName(SUMMARY_GOODS_NAME);
        summary.setProduceCode(null);
        summary.setProduceTypeId(null);
        // 汇总记录不设置具体的单价等信息
        summary.setSalesQty(null);
        summary.setDelivyQty(null);
        summary.setBillMoney(null);
        summary.setDelivAmount(null);
        summary.setAmount(null);
        summary.setCostAmount(null);
        for (st_trd_customer_produce_day_info record : allRecords) {
            summary.setAmount(addAmount(summary.getAmount(), record.getAmount()));
            summary.setDelivAmount(addAmount(summary.getDelivAmount(), record.getDelivAmount()));
            summary.setBillMoney(addAmount(summary.getBillMoney(), record.getBillMoney()));
            summary.setCostAmount(addAmount(summary.getCostAmount(), record.getCostAmount()));
        }
        return summary;
    }

    private void enableUpdateSummary(st_trd_customer_produce_day_info input, st_trd_customer_produce_day_info exist,
                                     List<st_trd_customer_produce_day_info> recordsToInsert,
                                     List<st_trd_customer_produce_day_info> recordsToUpdate) {
        if (exist == null) {
            input.setCreated(TimeUtils.getCurrentTime());
            recordsToInsert.add(input);
            return;
        }
        boolean isNeedUpdate = false;
        if (input.getAmount() != null) {
            if (exist.getAmount() == null || input.getAmount().compareTo(exist.getAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setAmount(null);
            }
        }
        if (input.getDelivAmount() != null) {
            if (exist.getDelivAmount() == null || input.getDelivAmount().compareTo(exist.getDelivAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setDelivAmount(null);
            }
        }
        if (input.getBillMoney() != null) {
            if (exist.getBillMoney() == null || input.getBillMoney().compareTo(exist.getBillMoney()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setBillMoney(null);
            }
        }
        if (input.getCostAmount() != null) {
            if (exist.getCostAmount() == null || input.getCostAmount().compareTo(exist.getCostAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setCostAmount(null);
            }
        }
        if (isNeedUpdate) {
            input.setId(exist.getId());
            input.setUpdated(TimeUtils.getCurrentTime());
            recordsToUpdate.add(input);
        }
    }

    private st_trd_customer_produce_month_info createMonthSummary(Collection<st_trd_customer_produce_month_info> allRecords) {
        st_trd_customer_produce_month_info summary = new st_trd_customer_produce_month_info();
        st_trd_customer_produce_month_info anyOne = allRecords.stream().findAny().get();
        BeanUtils.copyProperties(anyOne, summary);
        summary.setId(null);
        summary.setProduceId(SUMMARY_GOODS_ID);
        summary.setProduceName(SUMMARY_GOODS_NAME);
        summary.setProduceCode(null);
        summary.setProduceTypeId(null);
        // 汇总记录不设置具体的单价等信息
        summary.setSalesQty(null);
        summary.setDelivyQty(null);
        summary.setBillMoney(null);
        summary.setDelivAmount(null);
        summary.setAmount(null);
        summary.setCostAmount(null);
        for (st_trd_customer_produce_month_info record : allRecords) {
            summary.setAmount(addAmount(summary.getAmount(), record.getAmount()));
            summary.setDelivAmount(addAmount(summary.getDelivAmount(), record.getDelivAmount()));
            summary.setBillMoney(addAmount(summary.getBillMoney(), record.getBillMoney()));
            summary.setCostAmount(addAmount(summary.getCostAmount(), record.getCostAmount()));
        }
        return summary;
    }

    private void enableUpdateSummary(st_trd_customer_produce_month_info input, st_trd_customer_produce_month_info exist,
                                     List<st_trd_customer_produce_month_info> recordsToInsert,
                                     List<st_trd_customer_produce_month_info> recordsToUpdate) {
        if (exist == null) {
            input.setCreated(TimeUtils.getCurrentTime());
            recordsToInsert.add(input);
            return;
        }
        boolean isNeedUpdate = false;
        if (input.getAmount() != null) {
            if (exist.getAmount() == null || input.getAmount().compareTo(exist.getAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setAmount(null);
            }
        }
        if (input.getDelivAmount() != null) {
            if (exist.getDelivAmount() == null || input.getDelivAmount().compareTo(exist.getDelivAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setDelivAmount(null);
            }
        }
        if (input.getBillMoney() != null) {
            if (exist.getBillMoney() == null || input.getBillMoney().compareTo(exist.getBillMoney()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setBillMoney(null);
            }
        }
        if (input.getCostAmount() != null) {
            if (exist.getCostAmount() == null || input.getCostAmount().compareTo(exist.getCostAmount()) > 0) {
                isNeedUpdate = true;
            } else {
                input.setCostAmount(null);
            }
        }
        if (isNeedUpdate) {
            input.setId(exist.getId());
            input.setUpdated(TimeUtils.getCurrentTime());
            recordsToUpdate.add(input);
        }
    }

    public BigDecimal addAmount(BigDecimal before, BigDecimal value) {
        if (value == null) {
            return before;
        }
        if (before == null) {
            return value;
        }
        return before.add(value);
    }

    @Override
    public void aggregateDayToMonthRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId) {
        if (dayInfoList == null || dayInfoList.isEmpty()) {
            log.debug("日统计数据列表为空，跳过汇总到月表");
            return;
        }

        Set<String> dateSet = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();

        for (st_trd_customer_produce_day_info data : dayInfoList) {
            customerIds.add(data.getCustomerId());
            dateSet.addAll(getAllDaysInMonth(data.getVisitDate()));
        }

        // 批量查询所有可能存在的记录
        LambdaQueryWrapper<st_trd_customer_produce_day_info> batchQuery = new LambdaQueryWrapper<>();
        batchQuery.eq(st_trd_customer_produce_day_info::getTenantId, tenantId)
                .in(st_trd_customer_produce_day_info::getCustomerId, customerIds)
                .in(st_trd_customer_produce_day_info::getVisitDate, dateSet)
                .eq(st_trd_customer_produce_day_info::getIsDeleted, 0);

        List<st_trd_customer_produce_day_info> existingRecords = dayInfoRepository.list(batchQuery);

        // 构建查询结果映射
        Map<String, st_trd_customer_produce_month_info> recordsMap = new HashMap<>();
        for (st_trd_customer_produce_day_info record : existingRecords) {
            String monthDate = convertDayToMonthDate(record.getVisitDate());
            String key = buildMonthRecordKey(monthDate, record.getCustomerId(), record.getProduceId());
            st_trd_customer_produce_month_info monthInfo = recordsMap.get(key);
            if (monthInfo == null) {
                recordsMap.put(key, convertDayInfoToMonthInfo(record, monthDate));
            } else {
                aggregateDayDataToMonthInfo(record, monthInfo);
            }
        }

        log.info("汇总月份的数据到月表，记录数：{}", recordsMap.size());

        // 按月份分组汇总日表数据
        saveMonthInfoRecords(new ArrayList<>(recordsMap.values()), tenantId, true);

        log.info("日表数据汇总到月表完成，共处理 {} 个数据", recordsMap.size());
    }

    private List<String> getAllDaysInMonth(String visitDate) {
        // 定义日期格式
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(visitDate, "yyyy-MM-dd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        int dayOfMonth = localDateTime.getMonth().length(localDateTime.getYear() % 4 ==0);
        List<String> result = Lists.newArrayList();
        // 循环输出每一天的日期
        for (int day = 1; day <= dayOfMonth; day++) {
            result.add(localDateTime.withDayOfMonth(day).format(formatter));
        }
        return result;
    }

    private String buildMonthRecordKey(String visitDate, Long customerId, Long produceId) {
        return visitDate + "_" + customerId + "_" + produceId;
    }

    /**
     * 将日期从 "2025-01-01" 格式转换为 "2025-01" 格式
     */
    private String convertDayToMonthDate(String dayDate) {
        if (dayDate == null || dayDate.length() < 7) {
            return dayDate;
        }
        return dayDate.substring(0, 7); // 取前7位：2025-01
    }

    /**
     * 将日表实体转换为月表实体
     */
    private st_trd_customer_produce_month_info convertDayInfoToMonthInfo(st_trd_customer_produce_day_info dayInfo, String monthDate) {
        st_trd_customer_produce_month_info monthInfo = new st_trd_customer_produce_month_info();

        // 复制基本属性
        BeanUtils.copyProperties(dayInfo, monthInfo);

        // 重置ID和时间字段
        monthInfo.setId(null);
        monthInfo.setVisitDate(monthDate);
        monthInfo.setCreated(TimeUtils.getCurrentTime());
        monthInfo.setUpdated(TimeUtils.getCurrentTime());
        monthInfo.setExtra(null);
        return monthInfo;
    }

    /**
     * 将同一客户同一产品的多条日表数据汇总为一条月表数据
     */
    private void aggregateDayDataToMonthInfo(st_trd_customer_produce_day_info dayInfo, st_trd_customer_produce_month_info monthInfo) {

        if (dayInfo.getAmount() != null) {
            if (monthInfo.getAmount() == null) {
                monthInfo.setAmount(dayInfo.getAmount());
            } else {
                monthInfo.setAmount(monthInfo.getAmount().add(dayInfo.getAmount()));
            }
        }
        if (dayInfo.getDelivAmount() != null) {
            if (monthInfo.getDelivAmount() == null) {
                monthInfo.setDelivAmount(dayInfo.getDelivAmount());
            } else {
                monthInfo.setDelivAmount(monthInfo.getDelivAmount().add(dayInfo.getDelivAmount()));
            }
        }
        if (dayInfo.getBillMoney() != null) {
            if (monthInfo.getBillMoney() == null) {
                monthInfo.setBillMoney(dayInfo.getBillMoney());
            } else {
                monthInfo.setBillMoney(monthInfo.getBillMoney().add(dayInfo.getBillMoney()));
            }
        }
        if (dayInfo.getCostAmount() != null) {
            if (monthInfo.getCostAmount() == null) {
                monthInfo.setCostAmount(dayInfo.getCostAmount());
            } else {
                monthInfo.setCostAmount(monthInfo.getCostAmount().add(dayInfo.getCostAmount()));
            }
        }
        if (dayInfo.getSalesQty() != null) {
            if (monthInfo.getSalesQty() == null) {
                monthInfo.setSalesQty(dayInfo.getSalesQty());
            } else {
                monthInfo.setSalesQty(monthInfo.getSalesQty().add(dayInfo.getSalesQty()));
            }
        }
        if (dayInfo.getDelivyQty() != null) {
            if (monthInfo.getDelivyQty() == null) {
                monthInfo.setDelivyQty(dayInfo.getDelivyQty());
            } else {
                monthInfo.setDelivyQty(monthInfo.getDelivyQty().add(dayInfo.getDelivyQty()));
            }
        }
    }

    /**
     * 批量处理器函数式接口
     */
    @FunctionalInterface
    private interface BatchProcessor<T> {
        /**
         * 处理一个批次的数据
         *
         * @param batch        批次数据
         * @param batchIndex   当前批次索引（从1开始）
         * @param totalBatches 总批次数
         */
        void process(List<T> batch, int batchIndex, int totalBatches);
    }
}
