package com.neo.nova.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.helper.ChartHelper;
import com.neo.nova.app.service.CustomerSalesService;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.app.vo.PieChartVO;
import com.neo.nova.app.vo.SalesReportQueryVO;
import com.neo.nova.domain.dto.*;
import com.neo.nova.domain.entity.*;
import com.neo.nova.domain.enums.*;
import com.neo.nova.domain.excelExport.StatisticsExport;
import com.neo.nova.domain.gateway.PerformancePlanRepository;
import com.neo.nova.domain.gateway.PerformanceTargetRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_month_infoRepository;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;


@Service
public class CustomerSalesServiceImpl implements CustomerSalesService {
    @Autowired
    private st_trd_customer_produce_day_infoRepository st_trd_customer_produce_day_infoRepository;
    @Autowired
    private st_trd_customer_produce_month_infoRepository st_trd_customer_produce_month_infoRepository;

    @Autowired
    private PerformancePlanRepository performancePlanRepository;
    @Autowired
    private PerformanceTargetRepository performanceTargetRepository;
    @Resource
    private MetricService metricService;

    /**
     * 销售额统计
     *
     * @param dataFormQueryVO
     * @return
     */
    @Override
    public DataFormVO statistics(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return null;
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 表格查询
        IPage<Map<String, Object>> tableResultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            tableResultMaps = st_trd_customer_produce_day_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            tableResultMaps = st_trd_customer_produce_month_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        }

        // 组装表格
        Table table = new Table();

        // 添加查询条件列
        columnMapping.forEach((key, value) ->
                table.addToFirstColumn(Column.builder().key(key).title(value).build())
        );

        List<Map<String, Object>> tableRecords = tableResultMaps.getRecords();
        batchMapIdToNames(tableRecords, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整
        // 填充表格数据
        tableRecords.forEach(map -> {
            table.addRow(Maps.newHashMap());
            map.forEach((key, value) -> {
                if (("amount".equals(key))) {
                    table.addToLastRow(key, Cell.builder().value(new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP)).build());
                } else {
                    table.addToLastRow(key, Cell.builder().value(value).build());
                }
            });
        });
        table.addToFirstColumn(Column.builder().key("amount").title("销售额").isSort(true).build());
        // 设置分页信息
        table.setPageSize(tableResultMaps.getSize());
        table.setTotalPage(tableResultMaps.getPages());
        table.setCurrentPage(tableResultMaps.getCurrent());
        table.setTotal(tableResultMaps.getTotal());

        // 组装返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);


        return dataFormVO;
    }

    private <T> void statistics_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperAssembly(wrapper, columnMapping, key, value));
        List<String> selectColumns = Lists.newArrayList(columnMapping.keySet());

        //先处理sort
        wrapper.orderBy(StringUtils.isBlank(dataFormQueryVO.getSortBy()), true, selectColumns);
        wrapper.orderBy(StringUtils.isNotBlank(dataFormQueryVO.getSortBy()), !"desc".equals(dataFormQueryVO.getSort()), dataFormQueryVO.getSortBy());

        selectColumns.add("sum(amount) as amount");
        wrapper.select(selectColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
                .ne("produceId", -1)  //过滤无效数据
        ;

        if (!selectColumns.contains("produceName") && !selectColumns.contains("produceTypeId")) {
            //查询条件中不带produceName和produceTypeId时 ，查询全店合计
            wrapper.eq("produceId", 0);
        } else {
            //查询商品时，过滤全店
            wrapper.ne("produceId", 0);
        }
    }

    /**
     * 销售趋势
     *
     * @param dataFormQueryVO
     * @return
     */
    @Override
    public DataFormVO trends(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return null;
        }
        //对时间进行合法校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        //table查询
        IPage<Map<String, Object>> tableResultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            tableResultMaps = st_trd_customer_produce_day_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            tableResultMaps = st_trd_customer_produce_month_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        }

        // 批量映射ID到中文名称
        List<Map<String, Object>> tableRecords = tableResultMaps.getRecords();
        batchMapIdToNames(tableRecords, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        //组装table
        Table table = new Table();
        table.addToFirstColumn(Column.builder().key("visit_date").title("日期").isSort(true).build());
        columnMapping.forEach((key, value) -> table.addToFirstColumn(Column.builder().key(key).title(value).build()));
        table.addToFirstColumn(Column.builder().key("amount").title("销售额").isSort(true).build());
        tableRecords.forEach(map -> {
            table.addRow(Maps.newHashMap());
            map.forEach((key, value) -> {
                if (("amount".equals(key))) {
                    table.addToLastRow(key, Cell.builder().value(new BigDecimal(value.toString()).setScale(2, RoundingMode.HALF_UP)).build());
                } else {
                    table.addToLastRow(key, Cell.builder().value(value).build());
                }
            });
        });
        table.setPageSize(tableResultMaps.getSize());
        table.setTotalPage(tableResultMaps.getPages());
        table.setCurrentPage(tableResultMaps.getCurrent());
        table.setTotal(tableResultMaps.getTotal());


        //查询chart
        List<Map<String, Object>> chartResultMaps;
        columnMapping = Maps.newLinkedHashMap();
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            chartResultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            chartResultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射chart数据的ID到中文名称
        batchMapIdToNames(chartResultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        //组装chart
        Chart<String, String> chart = ChartHelper.buildMultiDimensionChart(chartResultMaps, columnMapping.keySet(), "visit_date", "amount");


        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);
        dataFormVO.setChart(chart);
        return dataFormVO;

    }


    private static void timeConditionCheck(TimeCondition timeCondition) {
        if (timeCondition == null) {
            throw new BizCustomException(104, "时间条件不能为空");
        }
        //对periodType进行合法校验
        Integer periodType = timeCondition.getPeriodType();
        if (Arrays.stream(PeriodTypeEnum.values()).map(PeriodTypeEnum::getCode).noneMatch(code -> code.equals(periodType))) {
            throw new BizCustomException(100, "无效的周期类型: " + periodType);
        }
        //对时间进行校验
        String startDate = timeCondition.getStartDate();
        String endDate = timeCondition.getEndDate();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            // 校验时间格式是否为 yyyy-MM-dd
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM-dd: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM-dd: " + endDate);
            }
        } else {
            // 校验时间格式是否为 yyyy-MM
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM: " + endDate);
            }
        }

        // 校验开始时间不能晚于结束时间
        if (startDate.compareTo(endDate) > 0) {
            throw new BizCustomException(103, "开始时间不能晚于结束时间");
        }

    }

    private <T> void _buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperAssembly(wrapper, columnMapping, key, value));
        List<String> selectColumns = Lists.newArrayList(columnMapping.keySet());

        //先处理sort
        wrapper.orderBy(StringUtils.isBlank(dataFormQueryVO.getSortBy()), false, "visit_date", selectColumns);
        wrapper.orderBy(StringUtils.isNotBlank(dataFormQueryVO.getSortBy()), !"desc".equals(dataFormQueryVO.getSort()), dataFormQueryVO.getSortBy());

        selectColumns.add("visit_date");
        selectColumns.add("sum(amount) as amount");
        wrapper.select(selectColumns)
                .groupBy("visit_date")
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
                .ne("produceId", -1)  //过滤无效数据
        ;

        if (!selectColumns.contains("produceName") && !selectColumns.contains("produceTypeId")) {
            //查询条件中不带produceName和produceTypeId时 ，查询全店合计
            wrapper.eq("produceId", 0);
        } else {
            //查询商品时，过滤全店
            wrapper.ne("produceId", 0);
        }
    }


    private static <T> void wrapperAssembly(QueryWrapper<T> wrapper, Map<String, String> columnMapping, String metricCode, List<String> values) {
        String selectColumn = MetricCodeEnum.metricCodeMapping(metricCode);
        columnMapping.put(selectColumn, MetricCodeEnum.getNameByCode(metricCode));
        wrapper.groupBy(selectColumn);
        valueSearchSet(wrapper, metricCode, selectColumn, values);
        //查询复杂指标时，去除空项
        if (MetricCodeEnum.isComplexMetric(metricCode)) {
            wrapper.ne(selectColumn, "").isNotNull(selectColumn);
        }
    }

    private static <T> void valueSearchSet(QueryWrapper<T> wrapper, String metricCode, String selectColumn, List<String> values) {
        if (CollectionUtil.isEmpty(values)) {
            return;
        }
        //简写搜索特判
        if (MetricCodeEnum.CUSTOMER_NAME.getCode().equals(metricCode)) {
            wrapper.nested(wq ->
                    wq.like(selectColumn, values.get(0))
                            .or()
                            .likeLeft("customerMnemoCode", values.get(0))
            );
            return;
        }
        if (MetricCodeEnum.PRODUCT_NAME.getCode().equals(metricCode)
                || MetricCodeEnum.CUSTOMER_OWNER.getCode().equals(metricCode)) {
            wrapper.like(selectColumn, values.get(0));
            return;
        }

        wrapper.in(selectColumn, values);
    }


    /**
     * 饼图统计
     *
     * @param currentTime 格式：YYYYMM，例如：202501
     * @return 饼图数据
     */
    @Override
    public PieChartVO pieChart(Long tenantId, String currentTime) {
        try {

            LocalDateTime localDateTime = LocalDateTimeUtil.parse(currentTime, "yyyy-MM");

            String yearStartMonth = localDateTime.getYear() + "-01";
            String yearEndMonth = localDateTime.getYear() + "-12";
            // 查询年度和月度计划
            PerformancePlan yearPlan = getYearPlan(tenantId, yearStartMonth);
            PerformancePlan monthPlan = getMonthPlan(tenantId, currentTime);

            // 获取目标值
            BigDecimal yearTargetValue = getTargetValue(yearPlan);
            BigDecimal monthTargetValue = getTargetValue(monthPlan);

            // 获取实际销售额
            BigDecimal yearActualValue = getAmount(tenantId, yearStartMonth, yearEndMonth);
            BigDecimal monthActualValue = getAmount(tenantId, currentTime, currentTime);

            // 计算时间进度
            TimeProgress timeProgress = calculateTimeProgress(localDateTime);

            // 构建返回结果
            PieChartVO pieChartVO = PieChartVO.builder()
                    .yearTargetValue(yearTargetValue)
                    .monthTargetValue(monthTargetValue)
                    .yearActualValue(yearActualValue)
                    .monthActualValue(monthActualValue)
                    .yearTimePercent(timeProgress.getYearTimePercent())
                    .monthTimePercent(timeProgress.getMonthTimePercent())
                    .build();

            // 计算实现率、状态和剩余目标
            pieChartVO.calculatePercents();
            pieChartVO.calculateStatus();
            pieChartVO.calculateRemainingTargets();

            return pieChartVO;

        } catch (BizCustomException e) {
            throw e;
        } catch (Exception e) {
            throw new BizCustomException(500, "饼图数据查询失败：" + e.getMessage());
        }
    }

    @Override
    public PerformanceProgressDTO getProgress(Long tenantId, List<Long> userIds, TimeCondition timeCondition) {
        if (timeCondition == null || timeCondition.getStartDate() == null) {
            return null;
        }
        if (PeriodTypeEnum.YEAR.getCode() != timeCondition.getPeriodType()) {
            PerformancePlan yearPlan = getYearPlan(tenantId, timeCondition.getStartDate());
            BigDecimal yearTargetValue = getTargetValue(yearPlan);
        }
        return null;
    }

    /**
     * 查询年度计划
     */
    private PerformancePlan getYearPlan(Long tenantId, String yearStartMonth) {
        LambdaQueryWrapper<PerformancePlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PerformancePlan::getPeriodStart, yearStartMonth)
                .eq(PerformancePlan::getPeriodType, 0) // 年度计划
                .eq(PerformancePlan::getIsDeleted, 0)
                .eq(PerformancePlan::getTenantId, tenantId)
                .last("LIMIT 1");

        return performancePlanRepository.getBaseMapper().selectOne(wrapper);
    }

    /**
     * 查询月度计划
     */
    private PerformancePlan getMonthPlan(Long tenantId, String currentTime) {
        LambdaQueryWrapper<PerformancePlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PerformancePlan::getPeriodStart, currentTime)
                .eq(PerformancePlan::getPeriodType, 4) // 月度计划
                .eq(PerformancePlan::getIsDeleted, 0)
                .eq(PerformancePlan::getTenantId, tenantId)
                .last("LIMIT 1");

        return performancePlanRepository.getBaseMapper().selectOne(wrapper);
    }

    /**
     * 获取计划的目标值
     */
    private BigDecimal getTargetValue(PerformancePlan plan) {
        if (plan == null) {
            return BigDecimal.ZERO;
        }
        try {
            return getMonthlyTarget(plan.getId());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算时间进度
     */
    private TimeProgress calculateTimeProgress(LocalDateTime localDateTime) {
        LocalDateTime now = LocalDateTimeUtil.now();

        // 未来
        if (now.getYear() > localDateTime.getYear() || now.getMonthValue() > localDateTime.getMonthValue()) {
            // 未来时间，进度为0
            return new TimeProgress(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        //过去
        if (now.getYear() < localDateTime.getYear() || now.getMonthValue() < localDateTime.getMonthValue()) {
            return new TimeProgress(BigDecimal.ONE, BigDecimal.ONE);
        }

        // 年度时间进度
        BigDecimal yearTimePercent = new BigDecimal(now.getMonthValue()).divide(new BigDecimal("12"), RoundingMode.HALF_DOWN);
        // 当前月份，按天计算
        BigDecimal monthTimePercent = new BigDecimal(now.getDayOfMonth()).divide(new BigDecimal(now.getMonth().length(now.getYear() % 4 == 0)), RoundingMode.HALF_DOWN);

        return new TimeProgress(yearTimePercent.setScale(4, RoundingMode.HALF_UP), monthTimePercent.setScale(4, RoundingMode.HALF_UP));
    }

    /**
     * 时间进度内部类
     */
    @Getter
    private static class TimeProgress {
        private final BigDecimal yearTimePercent;
        private final BigDecimal monthTimePercent;

        public TimeProgress(BigDecimal yearTimePercent, BigDecimal monthTimePercent) {
            this.yearTimePercent = yearTimePercent;
            this.monthTimePercent = monthTimePercent;
        }
    }


    public BigDecimal getAmount(Long tenantId, String startDate, String endDate) {
        // 创建查询包装器
        QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
        // 根据 metricCode 添加对应的查询条件
        // 添加时间范围条件
        queryWrapper.between("visit_date", startDate, endDate);
        // 使用 SQL SUM 函数查询总和
        queryWrapper.select("SUM(amount) AS totalAmount");
        // 只选择总金额
        queryWrapper.eq("isDeleted", 0).eq("tenantId", tenantId);
        // 执行查询
        List<Map<String, Object>> resultList = st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);

        // 处理结果
        if (resultList != null && !resultList.isEmpty()) {
            Map<String, Object> resultMap = resultList.get(0);

            // 防御性检查：确保 resultMap 不为 null
            if (resultMap != null && resultMap.containsKey("totalAmount")) {
                Object totalAmountObj = resultMap.get("totalAmount");

                // 关键检查：确保 totalAmountObj 不为 null
                if (totalAmountObj != null) {
                    try {
                        return new BigDecimal(totalAmountObj.toString());
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                }
            }
        }

        // 无结果或异常情况返回默认值
        return BigDecimal.ZERO;
    }

    public BigDecimal getMonthlyTarget(Long planId) {
        // 创建查询包装器
        QueryWrapper<PerformanceTarget> queryWrapper = new QueryWrapper<>();
        // 使用 SQL SUM 函数查询总和
        queryWrapper.select("SUM(targetValue) AS totalTargetValue");
        queryWrapper.eq("parentId", 0L);
        queryWrapper.eq("planId", planId);
        queryWrapper.eq("metricDataType", MetricDataTypeEnum.GMV.getCode());
        // 只选择总金额
        queryWrapper.eq("isDeleted", 0);
        // 执行查询
        List<Map<String, Object>> resultList = performanceTargetRepository.getBaseMapper().selectMaps(queryWrapper);
        // 处理结果
        if (resultList != null && !resultList.isEmpty()) {
            Map<String, Object> resultMap = resultList.get(0);
            // 防御性检查：确保 resultMap 不为 null
            if (resultMap != null && resultMap.containsKey("totalTargetValue")) {
                Object totalAmountObj = resultMap.get("totalTargetValue");
                if (totalAmountObj != null) {
                    try {
                        return new BigDecimal(totalAmountObj.toString());
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                }
            }
        }
        // 无结果或异常情况返回默认值
        return BigDecimal.ZERO;
    }


    @Override
    public List<StatisticsExport> StatisticsExport(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            statisticsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            statisticsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        // 转换为StatisticsExport对象列表
        List<StatisticsExport> exportList = new ArrayList<>();
        for (int i = 0; i < resultMaps.size(); i++) {
            Map<String, Object> map = resultMaps.get(i);
            StatisticsExport export = new StatisticsExport();

            // 设置序号
            export.setId((long) (i + 1));

            // 设置基本字段
            export.setProduceName(getStringValue(map, "produceName"));
            export.setProduceCode(getStringValue(map, "produceCode"));
            export.setCustomerName(getStringValue(map, "customerName"));
            export.setCustomerCode(getStringValue(map, "customerCode"));
            export.setCustomerLevel(getIntegerValue(map, "customerLevel"));
            export.setSalesName(getStringValue(map, "salesName"));
            export.setSalesCode(getStringValue(map, "salesCode"));
            export.setChannel(getStringValue(map, "channel"));
            // 暂时使用customerTypeId作为customerType，后续可以根据需要添加类型名称映射
            export.setCustomerType(getStringValue(map, "customerTypeId"));
            export.setAmount(getLongValue(map, "amount"));
            export.setVisitDate(getStringValue(map, "visit_date"));

            // 设置中文名称字段
            export.setChannelName(getStringValue(map, "channelName"));
            export.setSupermarketAreaName(getStringValue(map, "supermarketAreaName"));
            export.setAdminRegionName(getStringValue(map, "adminRegionName"));
            export.setCustomerLevelName(getStringValue(map, "customerLevelName"));
            export.setProduceTypeName(getStringValue(map, "produceTypeName"));

            exportList.add(export);
        }

        return exportList;
    }

    /**
     * 为Excel导出构建查询条件（不分页，获取所有数据）
     */
    private <T> void statisticsExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperAssembly(wrapper, columnMapping, key, value));

        // 选择需要的字段，包括分组字段和聚合字段
        List<String> selectColumns = Lists.newArrayList(columnMapping.keySet());
        selectColumns.add("produceName");
        selectColumns.add("produceCode");
        selectColumns.add("customerName");
        selectColumns.add("customerCode");
        selectColumns.add("customerLevel");
        selectColumns.add("customerTypeId");
        selectColumns.add("salesName");
        selectColumns.add("salesCode");
        selectColumns.add("channel");
        selectColumns.add("sum(amount) as amount");
        selectColumns.add("visit_date");

        // 构建GROUP BY子句，包含所有非聚合字段
        List<String> groupByColumns = Lists.newArrayList(columnMapping.keySet());
        groupByColumns.add("produceName");
        groupByColumns.add("produceCode");
        groupByColumns.add("customerName");
        groupByColumns.add("customerCode");
        groupByColumns.add("customerLevel");
        groupByColumns.add("customerTypeId");
        groupByColumns.add("salesName");
        groupByColumns.add("salesCode");
        groupByColumns.add("channel");
        groupByColumns.add("visit_date");

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate());
    }

    /**
     * 安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Override
    public List<StatisticsExport> trendsExport(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            trendsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            trendsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        // 转换为StatisticsExport对象列表
        List<StatisticsExport> exportList = new ArrayList<>();
        for (int i = 0; i < resultMaps.size(); i++) {
            Map<String, Object> map = resultMaps.get(i);
            StatisticsExport export = new StatisticsExport();

            // 设置序号
            export.setId((long) (i + 1));

            // 设置固定字段：时间和销售额
            export.setVisitDate(getStringValue(map, "visit_date"));
            export.setAmount(getLongValue(map, "amount"));

            // 动态设置条件列的值
            setDynamicColumns(export, map, columnMapping);

            // 设置中文名称字段
            export.setChannelName(getStringValue(map, "channelName"));
            export.setSupermarketAreaName(getStringValue(map, "supermarketAreaName"));
            export.setAdminRegionName(getStringValue(map, "adminRegionName"));
            export.setCustomerLevelName(getStringValue(map, "customerLevelName"));
            export.setProduceTypeName(getStringValue(map, "produceTypeName"));

            exportList.add(export);
        }

        return exportList;
    }

    /**
     * 为趋势导出构建查询条件（动态分组，不过滤数据）
     */
    private <T> void trendsExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        // 处理查询条件，但不进行数据过滤，只用于分组
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> {
            String selectColumn = MetricCodeEnum.metricCodeMapping(key);
            if (selectColumn != null) {
                columnMapping.put(selectColumn, MetricCodeEnum.getNameByCode(key));
            }
        });

        // 构建SELECT子句：固定列 + 动态条件列 + 聚合列
        List<String> selectColumns = Lists.newArrayList();
        selectColumns.add("visit_date");
        selectColumns.addAll(columnMapping.keySet()); // 添加动态条件列
        selectColumns.add("sum(amount) as amount");

        // 构建GROUP BY子句：时间 + 动态条件列
        List<String> groupByColumns = Lists.newArrayList();
        groupByColumns.add("visit_date");
        groupByColumns.addAll(columnMapping.keySet()); // 按条件列分组

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .orderBy(true, true, "visit_date"); // 按时间排序
    }

    /**
     * 动态设置条件列的值
     */
    private void setDynamicColumns(StatisticsExport export, Map<String, Object> map, Map<String, String> columnMapping) {
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            String columnName = entry.getKey();
            String columnTitle = entry.getValue();
            Object value = map.get(columnName);

            // 根据列名设置对应的字段值
            switch (columnName) {
                case "produceName":
                    export.setProduceName(getStringValue(map, columnName));
                    break;
                case "produceCode":
                    export.setProduceCode(getStringValue(map, columnName));
                    break;
                case "customerName":
                    export.setCustomerName(getStringValue(map, columnName));
                    break;
                case "customerCode":
                    export.setCustomerCode(getStringValue(map, columnName));
                    break;
                case "customerLevel":
                    export.setCustomerLevel(getIntegerValue(map, columnName));
                    break;
                case "customerTypeId":
                    export.setCustomerType(getStringValue(map, columnName));
                    break;
                case "salesName":
                    export.setSalesName(getStringValue(map, columnName));
                    break;
                case "salesCode":
                    export.setSalesCode(getStringValue(map, columnName));
                    break;
                case "channel":
                    export.setChannel(getStringValue(map, columnName));
                    break;
                default:
                    // 对于其他字段，可以根据需要扩展
                    break;
            }
        }
    }

    @Override
    public List<Map<String, Object>> getDynamicExportData(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            dynamicExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            dynamicExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        return resultMaps;
    }

    /**
     * 为动态导出构建查询条件（只分组，不过滤）
     */
    private <T> void dynamicExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        // 处理查询条件，但不进行数据过滤，只用于分组
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> {
            String selectColumn = MetricCodeEnum.metricCodeMapping(key);
            if (selectColumn != null) {
                columnMapping.put(selectColumn, MetricCodeEnum.getNameByCode(key));
            }
        });

        // 构建SELECT子句：固定列 + 动态条件列 + 聚合列
        List<String> selectColumns = Lists.newArrayList();
        selectColumns.add("visit_date");
        selectColumns.addAll(columnMapping.keySet()); // 添加动态条件列
        selectColumns.add("sum(amount) as amount");

        // 构建GROUP BY子句：时间 + 动态条件列
        List<String> groupByColumns = Lists.newArrayList();
        groupByColumns.add("visit_date");
        groupByColumns.addAll(columnMapping.keySet()); // 按条件列分组

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .orderBy(true, true, "visit_date"); // 按时间排序
    }

    /**
     * 批量映射ID到中文名称
     */
    private void batchMapIdToNames(List<Map<String, Object>> resultMaps, Long tenantId) {
        if (resultMaps == null || resultMaps.isEmpty()) {
            return;
        }

        // 收集所有需要映射的ID
        Set<Long> customerSalesRegionMap = new HashSet<>();
        Set<Long> adminRegionIds = new HashSet<>();
        Set<Long> produceTypeIds = new HashSet<>();
        Set<Long> customerTypeIds = new HashSet<>();
        Set<Long> salesId = new HashSet<>();

        for (Map<String, Object> map : resultMaps) {
            collectId(map, "salesRegionId", customerSalesRegionMap);
            collectId(map, "adminRegionId", adminRegionIds);
            collectId(map, "produceTypeId", produceTypeIds);
            collectId(map, "customerTypeId", customerTypeIds);
            collectId(map, "salesId", salesId);
        }

        // 批量查询名称映射
        Map<Long, String> customerSalesRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), customerSalesRegionMap);
        Map<Long, String> adminRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_ADMIN_REGION.getCode(), adminRegionIds);
        Map<Long, String> produceTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.PRODUCT_TYPE.getCode(), produceTypeIds);
        Map<Long, String> customerTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_TYPE.getCode(), customerTypeIds);
        Map<Long, String> salesNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_OWNER.getCode(), salesId);

        // 将名称映射回原始数据
        for (Map<String, Object> map : resultMaps) {
            Optional.ofNullable(map.get("channel")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("channel", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("supermarketAreaId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("supermarketAreaId", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("customerLevel")).map(String::valueOf).map(Long::valueOf).map(CustomerLevelEnum::getById)
                    .ifPresent(customerLevelEnum -> map.put("customerLevel", customerLevelEnum.getDesc()));
            mapIdToName(map, "salesRegionId", "salesRegionId", customerSalesRegionNameMap);
            mapIdToName(map, "adminRegionId", "adminRegionId", adminRegionNameMap);
            mapIdToName(map, "produceTypeId", "produceTypeId", produceTypeNameMap);
            mapIdToName(map, "customerTypeId", "customerTypeId", customerTypeNameMap);
            mapIdToName(map, "salesId", "salesId", salesNameMap);

        }
    }

    /**
     * 从Map中收集指定字段的ID
     */
    private void collectId(Map<String, Object> map, String fieldName, Set<Long> idSet) {
        Object value = map.get(fieldName);
        if (value != null) {
            try {
                Long id = Long.valueOf(value.toString());
                idSet.add(id);
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    /**
     * 将ID映射为名称并添加到Map中
     */
    private void mapIdToName(Map<String, Object> map, String idFieldName, String nameFieldName, Map<Long, String> nameMap) {
        Object idValue = map.get(idFieldName);
        if (idValue != null) {
            try {
                Long id = Long.valueOf(idValue.toString());
                String name = nameMap.get(id);
                if (name != null) {
                    map.put(nameFieldName, name);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    @Override
    public DataFormVO salesReport(SalesReportQueryVO queryVO) {
        if (queryVO == null || queryVO.getChannelMetricCodeId() == null ||
                queryVO.getTimeCondition() == null || queryVO.getTimeCondition().getStartDate() == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        // 构建查询条件
        QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
        wrapper.select(
                        "adminRegionId",
                        "salesName",
                        "customerName",
                        "sum(amount) as actualSalesAmount",
                        "sum(costAmount) as orderAmount",
                        "(sum(amount) - sum(costAmount)) as grossProfitAmount"
                )
                .eq("visit_date", queryVO.getTimeCondition().getStartDate())
                .eq("tenantId", queryVO.getTenantId())
                .eq("channel", queryVO.getChannelMetricCodeId())
                .eq("isDeleted", 0)
                .ne("produceId", 0) // 排除汇总记录
                .groupBy("adminRegionId", "salesName", "customerName")
                .orderBy(true, true, "adminRegionId", "salesName", "customerName");

        // 分页查询
        List<Map<String, Object>> records = st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(wrapper);

        // 批量映射ID到中文名称
        batchMapIdToNames(records, queryVO.getTenantId());

        // 构建表格
        Table table = new Table();
        table.setTitle("销售报表");

        // 添加表头
        table.addToFirstColumn(Column.builder().key("adminRegionId").title("区域").isFixed(true).build());
        table.addToFirstColumn(Column.builder().key("salesName").title("负责人").isFixed(true).build());
        table.addToFirstColumn(Column.builder().key("customerName")
                .title(Objects.equals(MetricCodeIdEnum.CHANNEL_MARKET.getCode(), queryVO.getChannelMetricCodeId()) ? "门店" : "客户名称").isFixed(true).build());
        table.addToFirstColumn(Column.builder().key("grossProfitAmount").title("毛利额").isSort(true).build());
        table.addToFirstColumn(Column.builder().key("orderAmount").title("订货额").isSort(true).build());
        table.addToFirstColumn(Column.builder().key("actualSalesAmount").title("实际销售额").isSort(true).build());

        // 填充数据
        records.forEach(record -> {
            table.addRow(Maps.newHashMap());
            table.addToLastRow("adminRegionId", Cell.builder().value(record.get("adminRegionId")).build());
            table.addToLastRow("salesName", Cell.builder().value(record.get("salesName")).build());
            table.addToLastRow("customerName", Cell.builder().value(record.get("customerName")).build());
            Object grossProfitAmount = record.get("grossProfitAmount");
            if (grossProfitAmount != null) {
                table.addToLastRow("grossProfitAmount", Cell.builder().value(new BigDecimal(grossProfitAmount.toString()).setScale(2, RoundingMode.HALF_UP)).build());
            }
            Object orderAmount = record.get("orderAmount");
            if (orderAmount != null) {
                table.addToLastRow("orderAmount", Cell.builder().value(new BigDecimal(orderAmount.toString()).setScale(2, RoundingMode.HALF_UP)).build());
            }
            Object actualSalesAmount = record.get("actualSalesAmount");
            if (actualSalesAmount != null) {
                table.addToLastRow("actualSalesAmount", Cell.builder().value(new BigDecimal(actualSalesAmount.toString()).setScale(2, RoundingMode.HALF_UP)).build());
            }

        });

        // 设置分页信息
        table.setPageSize((long) records.size());
        table.setTotalPage(1L);
        table.setCurrentPage(1L);
        table.setTotal((long) records.size());

        // 构建返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);

        return dataFormVO;
    }


}
