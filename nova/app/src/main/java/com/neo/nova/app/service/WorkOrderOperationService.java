package com.neo.nova.app.service;/*
 *
 *
 *                                                    __----~~~~~~~~~~~------___
 *                                   .  .   ~~//====......          __--~ ~~
 *                   -.            \_|//     |||\\  ~~~~~~::::... /~
 *                ___-==_       _-~o~  \/    |||  \\            _/~~-
 *        __---~~~.==~||\=_    -_--~/_-~|-   |\\   \\        _/~
 *    _-~~     .=~    |  \\-_    '-~7  /-   /  ||    \      /
 *  .~       .~       |   \\ -_    /  /-   /   ||      \   /
 * /  ____  /         |     \\ ~-_/  /|- _/   .||       \ /
 * |~~    ~~|--~~~~--_ \     ~==-/   | \~--===~~        .\
 *          '         ~-|      /|    |-~\~~       __--~~
 *                      |-~~-_/ |    |   ~\_   _-~            /\
 *                           /  \     \__   \/~                \__
 *                       _--~ _/ | .-~~____--~-/                  ~~==.
 *                      ((->/~   '.|||' -_|    ~~-/ ,              . _||
 *                                 -_     ~\      ~~---l__i__i__i--~~_/
 *                                 _-~-__   ~)  \--______________--~~
 *                               //.-~~~-~_--~- |-------~~~~~~~~
 *                                      //.-~~~--\
 *                               神兽保佑
 *
 *
 * 用途说明: 实现类
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 15:16
 */

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.dto.WorkOrderDetailDto;
import com.neo.nova.domain.dto.WorkOrderDto;

import java.util.List;

public interface WorkOrderOperationService {
    /**
     * 根据id查询工单信息
     *
     * @param id
     * @return
     */
    WorkOrderDto getWorkOrderById(Long id);

    /**
     * 根据工单id查询工单详情
     *
     * @param workOrderId
     * @return
     */
    List<WorkOrderDetailDto> getWorkOrderDetailByWorkOrderId(Long workOrderId);

    /**
     * 查询工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    PageResponse<WorkOrderListItemDTO> queryWorkOrders(WorkOrderQueryRequest request);

    /**
     * 获取工单详情
     *
     * @param workOrderId 工单ID
     * @return 工单详情响应
     */
    SingleResponse<WorkOrderDetailResponse> getWorkOrderDetail(Long workOrderId);

    /**
     * 通过工单详情id查询工单详情
     */
    WorkOrderDetailDto getWorkOrderDetailById(Long workOrderDetailId);

    /**
     * 获取拜访详情
     *
     * @param workOrderDetailId 工单详情ID
     * @return 拜访详情响应
     */
    SingleResponse<SimpleDetailResponse> getVisitDetail(Long workOrderDetailId);

    /**
     * 获取导购日常打卡详情
     *
     * @param workOrderId 工单ID
     * @return 打卡详情响应
     */
    SingleResponse<CheckInDetailResponse> getCheckInDetail(Long workOrderId);

    /**
     * 获取工单类型列表
     *
     * @return 工单类型响应
     */
    SingleResponse<WorkOrderTypeResponse> getWorkOrderTypes();

    /**
     * 根据工单类型获取子类型列表
     *
     * @param actionName 工单类型名称
     * @return 工单子类型响应
     */
    SingleResponse<WorkOrderSubTypeResponse> getWorkOrderSubTypes(String actionName);
    /**
     * 更新工单和工单详情
     *
     * @param actionTaskModel 工单任务模型
     * @return 更新结果响应
     */
    SingleResponse<Boolean> updateWorkOrderAndDetail(ActionTaskModel actionTaskModel);

    List<WorkOrderDetailDto> getWorkOrderDetailByCustomerId(Long CustomerId);
}
