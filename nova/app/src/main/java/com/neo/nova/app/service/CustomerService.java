package com.neo.nova.app.service;

import com.neo.api.Response;
import com.neo.nova.app.vo.CustomerDetailVo;
import com.neo.nova.app.vo.CustomerQueryVO;
import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.dto.CustomerInfoDTO;
import com.neo.nova.domain.dto.CustomerRemarkDTO;
import com.neo.nova.domain.entity.CustomerInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface CustomerService {
    CustomerInfoDTO list(CustomerQueryVO customerQueryVO);

    CustomerInfoDTO myList(CustomerQueryVO customerQueryVO);

    CustomerInfoDTO highSeasList(CustomerQueryVO customerQueryVO);

    //精准查询，不模糊匹配
    List<CustomerInfo> search(CustomerQueryVO customerQueryVO);

    //精准查询，不模糊匹配
    List<CustomerDTO> searchDTO(CustomerQueryVO customerQueryVO);

    CustomerInfo getById(Long id);

    List<CustomerInfo> listByIds(Collection<Long> ids);

    CustomerDetailVo getCustomerDetail(Long customerId);

    Boolean addAndUpdateCustomer(CustomerDTO customerDTO);

}
