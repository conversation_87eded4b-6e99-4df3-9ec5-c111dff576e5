package com.neo.nova.app.action.config;

import com.neo.nova.app.action.actions.*;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.RoleEnums;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.neo.nova.app.action.actionType.CheckInEnums.*;
import static com.neo.nova.app.action.actionType.PhotoRecordEnums.CLOTHES_PHOTO;
import static com.neo.nova.app.action.actionType.PhotoRecordEnums.GOODS_PHOTO;
import static com.neo.nova.app.action.actionType.ReportEnums.DAY_REPORT;
import static com.neo.nova.app.action.enums.RoleEnums.*;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 不通角色对应的动作行为
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 20:16
 */
@Component
public class RoleConfig {
    @Resource
    private CheckInAction checkInAction;
    @Resource
    private ReportAction reportAction;
    @Resource
    private ExceptionalAppealAction exceptionalAppealAction;
    @Resource
    private PhotoRecordAction photoRecordAction;
    @Resource
    private VisitingRecordAction visitingRecordAction;
    @Resource
    private NoticeAction noticeAction;
    @Resource
    private OrderMessageAlarmAction orderMessageAlarmAction;
    @Resource
    private CheckAndFixAction checkAndFixAction;


    public final static Map<RoleEnums, List<ActionConfig>> actionRoleConfig = new HashMap<>();

    public final static Map<String, ActionConfig> actionConfig = new HashMap<>();

    public final static Map<String, ActionConfig> actionAuthConfig = new HashMap<>();

    @PostConstruct
    private void init() {
        initRoleAction();
        initAction();
        initActionAuth();
    }

    /**
     * 初始化行为权限
     */
    private void initActionAuth() {
        actionAuthConfig.put("common", new ActionConfig(checkInAction, 1,0));
        actionAuthConfig.put("checkIn", new ActionConfig(checkInAction, 1,1));
        actionAuthConfig.put("report", new ActionConfig(reportAction, 1,2));
        actionAuthConfig.put("checkAndFix", new ActionConfig(checkAndFixAction, 1,3));
        actionAuthConfig.put("photoRecord", new ActionConfig(photoRecordAction, 1,4));
        actionAuthConfig.put("visitingRecord", new ActionConfig(visitingRecordAction, 1,5));
        actionAuthConfig.put("exceptionalAppeal", new ActionConfig(exceptionalAppealAction, 1,6));
        actionAuthConfig.put("orderMessageAlarm", new ActionConfig(orderMessageAlarmAction, 1,7));
        actionAuthConfig.put("notice", new ActionConfig(noticeAction, 1,8));
    }

    /**
     * 初始化组件信息
     */
    private void initAction() {
        actionConfig.put(ActionRelationEnums.CHECK_IN.getActionName(), new ActionConfig(checkInAction, 1,1));
        actionConfig.put(ActionRelationEnums.REPORT.getActionName(), new ActionConfig(reportAction, 1,1));
        actionConfig.put(ActionRelationEnums.CHECK_AND_FIX.getActionName(), new ActionConfig(checkAndFixAction, 1,1));
        actionConfig.put(ActionRelationEnums.PHOTO_RECORD.getActionName(), new ActionConfig(photoRecordAction, 1,1));
        actionConfig.put(ActionRelationEnums.VISITING_RECORD.getActionName(), new ActionConfig(visitingRecordAction, 1,1));
        actionConfig.put(ActionRelationEnums.EXCEPTIONAL_APPEAL.getActionName(), new ActionConfig(exceptionalAppealAction, 1,1));
        actionConfig.put(ActionRelationEnums.ORDER_MESSAGE_ALARM.getActionName(), new ActionConfig(orderMessageAlarmAction, 1,1));
        actionConfig.put(ActionRelationEnums.NOTICE.getActionName(), new ActionConfig(noticeAction, 1,1));
    }

    /**
     * 初始化角色权限
     */
    private void initRoleAction() {
        actionRoleConfig.put(CUSTOMER_SALESMAN, getActionConfig(CUSTOMER_SALESMAN));
        actionRoleConfig.put(CUSTOMER_DIRECTOR, getActionConfig(CUSTOMER_DIRECTOR));
        actionRoleConfig.put(CUSTOMER_SALESMAN_DIRECTOR, getActionConfig(CUSTOMER_SALESMAN_DIRECTOR));
        actionRoleConfig.put(CUSTOMER_SUPERVISOR, getActionConfig(CUSTOMER_SUPERVISOR));
        actionRoleConfig.put(CUSTOMER_SALESMAN_SUPERVISOR, getActionConfig(CUSTOMER_SALESMAN_SUPERVISOR));
    }

    /**
     * 获取角色对应的操作权限和配置动作
     *
     * @param roleEnums
     * @return
     */
    private List<ActionConfig> getActionConfig(RoleEnums roleEnums) {
        switch (roleEnums) {
            case CUSTOMER_SALESMAN:
                List<ActionConfig> csResult = new ArrayList<>();
                csResult.add( new ActionConfig(checkInAction, 0,CHECK_IN_AM.getCheckInType()));
                csResult.add( new ActionConfig(checkInAction, 1,CHECK_OUT_AM.getCheckInType()));
                csResult.add( new ActionConfig(checkInAction, 2,CHECK_IN_PM.getCheckInType()));
                csResult.add( new ActionConfig(checkInAction, 3,CHECK_OUT_PM.getCheckInType()));
                csResult.add( new ActionConfig(visitingRecordAction, 4,0));
                csResult.add( new ActionConfig(orderMessageAlarmAction, 5,1));
                csResult.add( new ActionConfig(reportAction, 6,DAY_REPORT.getType()));
                csResult.add( new ActionConfig(exceptionalAppealAction, 7,0));
                return csResult;
            case CUSTOMER_DIRECTOR:
                List<ActionConfig> cdResult = new ArrayList<>();
                cdResult.add( new ActionConfig(reportAction, 2,DAY_REPORT.getType()));
                return cdResult;
            case CUSTOMER_SALESMAN_DIRECTOR:
                List<ActionConfig> csdResult = new ArrayList<>();
                csdResult.add( new ActionConfig(checkInAction, 0, CHECK_IN_AM.getCheckInType()));
                csdResult.add( new ActionConfig(photoRecordAction, 1,CLOTHES_PHOTO.getType()));
                csdResult.add( new ActionConfig(photoRecordAction, 2, GOODS_PHOTO.getType()));
                csdResult.add( new ActionConfig(checkInAction, 3, CHECK_OUT_AM.getCheckInType()));
                csdResult.add( new ActionConfig(checkInAction, 4, CHECK_IN_PM.getCheckInType()));
                csdResult.add( new ActionConfig(photoRecordAction, 5, CLOTHES_PHOTO.getType()));
                csdResult.add( new ActionConfig(photoRecordAction, 6, GOODS_PHOTO.getType()));
                csdResult.add( new ActionConfig(checkInAction, 7, CHECK_OUT_PM.getCheckInType()));
                return csdResult;
            case CUSTOMER_SUPERVISOR:
                List<ActionConfig> csuResult = new ArrayList<>();
                csuResult.add( new ActionConfig(checkInAction, 0, CHECK_IN_AM.getCheckInType()));
                csuResult.add( new ActionConfig(checkAndFixAction, 1,0));
                csuResult.add( new ActionConfig(checkInAction, 2, CHECK_OUT_PM.getCheckInType()));
                csuResult.add( new ActionConfig(reportAction, 3,DAY_REPORT.getType()));
                csuResult.add( new ActionConfig(exceptionalAppealAction, 4,0));
                return csuResult;

            case CUSTOMER_SALESMAN_SUPERVISOR:


        }
        return null;
    }

}
