package com.neo.nova.app.vo;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class CustomerRemarkVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 跟进类型
     */
    private Integer type;

    /**
     * 跟进记录内容
     */
    private String content;
    /**
     * 跟进时间
     */
    private String followUpTime;

    /**
     * 关联记录ID
     */
    private Long recordId;
    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 创建时间
     */
    private String createTime;
}
