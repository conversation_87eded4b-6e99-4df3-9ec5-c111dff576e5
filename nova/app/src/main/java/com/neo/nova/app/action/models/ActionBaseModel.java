package com.neo.nova.app.action.models;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

import java.util.Map;
import java.util.Objects;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 动作基类
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 16:28
 */

@Data
public class ActionBaseModel {
    private Long userId;
    private Long workOrderId;
    private Long workOrderDetailId;
    private String extra;
    private Map<String, Object> extraInfo;
    private String actionName;
    private String exceptionMessage;
}
