package com.neo.nova.app.action.actions;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.VisitingRecordModel;
import com.neo.nova.app.action.records.VisitingRecord;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.neo.nova.app.action.enums.WorkOrderDetailStatusEnums.COMPLETED;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/11 11:03
 */
@Slf4j
@Component
public class VisitingRecordAction extends AbstractBaseAction {
    @Autowired
    private IWorkOrderDetailRepository workOrderDetailRepository;
    @Autowired
    private IWorkOrderRepository workOrderRepository;

    @Autowired
    private CustomerRemarkService customerRemarkService;

    //private VisitingRecordModel params;

    /**
     * 获取动作名称
     *
     * @return
     */
    @Override
    public String getActionName() {
        return "visitingRecord";
    }

    /**
     * 参数验证
     *
     * @param actionBaseModel
     * @return
     */
    @Override
    public Boolean paramsVerification(ActionBaseModel actionBaseModel) {
        ActionBaseModel params = actionBaseModel;

        // 验证必要参数是否为空
        if (params.getWorkOrderId() == null) {
            params.setExceptionMessage("工单ID不能为空");
            return false;
        }
        if (params.getUserId() == null) {
            params.setExceptionMessage("用户ID不能为空");
            return false;
        }


        return true;
    }

    /**
     * 基础执行类
     *
     * @param actionBaseModel
     */
    @Override
    public boolean execute(ActionBaseModel actionBaseModel) {

        ActionBaseModel params = actionBaseModel;
        LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderDetail::getWorkOrderId, params.getWorkOrderId());
        WorkOrderDetail workOrderDetail = workOrderDetailRepository.getOne(queryWrapper);


        Map<String, Object> actionDetail = new HashMap<>();

        //不要丢了原来的东西
        if(StringUtils.hasText(workOrderDetail.getActionDetail())){
            actionDetail.putAll(JSONObject.parseObject(workOrderDetail.getActionDetail(), Map.class));
        }

        //添加新的
        if(MapUtils.isNotEmpty(actionBaseModel.getExtraInfo())){
            actionDetail.putAll(actionBaseModel.getExtraInfo());
        }

        workOrderDetail.setActionDetail(JSON.toJSONString(actionDetail));

        //状态判定
        if(actionDetail.get("content") != null && actionDetail.get("outTime") != null){
            workOrderDetail.setStatus(COMPLETED.getCode());
            workOrderDetail.setCompleteTime(LocalDateTime.now());
        } else {
            workOrderDetail.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
        }


        //接入企业的实践
        customerRemarkService.addCustomerRemarkByAction(workOrderDetail);

        return workOrderDetailRepository.updateById(workOrderDetail);
    }

    /**
     * @param actionTaskModel 
     * @return
     */
    @Override
    public boolean createTask(ActionTaskModel actionTaskModel) {

        Long workOrderId = actionTaskModel.getWorkOrderId();
        if (workOrderId != null) {
//            WorkOrder workOrder = workOrderRepository.getById(workOrderId);
//            if (workOrder == null) {
//                log.error("拜访记录: 无此工单");
//                params.setExceptionMessage("拜访记录: 无此工单");
//                return false;
//            }
            // 初始化工单
            WorkOrderDetail workOrderDetail = new WorkOrderDetail();
            workOrderDetail.setWorkOrderId(workOrderId);
            workOrderDetail.setAction(actionTaskModel.getActionName());
            workOrderDetail.setExecutorId(actionTaskModel.getUserId());
            workOrderDetail.setType(actionTaskModel.getWorkOrderDetailType());
            workOrderDetail.setCreatorId(SessionContextHolder.getUserId());
            workOrderDetail.setSort(0);
            workOrderDetail.setActionDetail(JSON.toJSONString(actionTaskModel.getActionDetail()));
            workOrderDetail.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
            workOrderDetail.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
            workOrderDetail.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
            return workOrderDetailRepository.save(workOrderDetail);
        }
        return false;
    }
}
