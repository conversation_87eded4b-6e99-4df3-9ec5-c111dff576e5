package com.neo.nova.app.service;

import com.neo.api.PageResponse;
import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.nova.app.request.DataSourceListRequest;
import com.neo.nova.app.request.DataSourceUploadRequest;
import com.neo.nova.app.vo.DataSourceConfigVO;
import com.neo.nova.domain.entity.DataSourceConfig;

import java.util.List;

/**
 * 数据导入服务接口
 */
public interface DataSourceService {

    /**
     * 列表查询
     *
     * @param dataSourceListRequest 查询参数
     * @return
     */
    PageResponse<DataSourceConfigVO> list(DataSourceListRequest dataSourceListRequest);

    /**
     * 处理数据导入
     *
     * @param dataSourceUploadRequest 入参
     * @return 处理结果
     */
    SingleResponse<Long> processDataImport(DataSourceUploadRequest dataSourceUploadRequest);

    /**
     * 更新数据源状态
     *
     * @param dataSourceConfig
     * @return
     */
    boolean updateDataSource(DataSourceConfig dataSourceConfig);
}
