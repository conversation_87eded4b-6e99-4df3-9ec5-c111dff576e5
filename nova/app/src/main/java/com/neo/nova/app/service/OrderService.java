package com.neo.nova.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.vo.OrderQueryVO;
import com.neo.nova.domain.dto.OrderDTO;

import java.util.List;

/**
 * 订单服务接口
 */
public interface OrderService {

    /**
     * 创建订单
     *
     * @param orderDTO 订单信息
     * @return 创建结果
     */
    SingleResponse<OrderDTO> createOrder(OrderDTO orderDTO);

    /**
     * 更新订单
     *
     * @param orderDTO 订单信息
     * @return 更新结果
     */
    SingleResponse<Boolean> updateOrder(OrderDTO orderDTO);

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    SingleResponse<Boolean> deleteOrder(Integer id);

    /**
     * 获取订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    SingleResponse<OrderDTO> getOrderById(Integer id);

    /**
     * 分页查询订单
     *
     * @param queryVO 查询条件
     * @return 订单列表
     */
    PageResponse<OrderDTO> pageOrders(OrderQueryVO queryVO);

    /**
     * 批量删除订单
     *
     * @param ids 订单ID列表
     * @return 删除结果
     */
    SingleResponse<Boolean> batchDeleteOrders(List<Integer> ids);

    /**
     * 更新订单状态
     *
     * @param id 订单ID
     * @param status 订单状态
     * @return 更新结果
     */
    SingleResponse<Boolean> updateOrderStatus(Integer id, Integer status);

    /**
     * 根据客户ID查询订单
     *
     * @param customerId 客户ID
     * @param pageIndex 页码（可选，默认1）
     * @param pageSize 页大小（可选，默认10）
     * @param includeDetails 是否包含订单明细（可选，默认false）
     * @return 订单列表
     */
    PageResponse<OrderDTO> getOrdersByCustomerId(Long customerId, Integer pageIndex, Integer pageSize, Boolean includeDetails);
}
