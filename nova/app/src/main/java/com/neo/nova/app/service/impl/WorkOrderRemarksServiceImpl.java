package com.neo.nova.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.service.WorkOrderRemarksService;
import com.neo.nova.app.vo.WorkOrderRemarkVO;
import com.neo.nova.domain.dto.WorkOrderRemarkDTO;
import com.neo.nova.domain.entity.WorkOrderRemarks;
import com.neo.nova.domain.gateway.IWorkOrderRemarksRepository;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


@Service
public class WorkOrderRemarksServiceImpl implements WorkOrderRemarksService {
    @Autowired
    private IWorkOrderRemarksRepository workOrderRemarksRepository;

    /**
     * 增加工单备注
     * @param workOrderRemarkVO
     * @return
     */
    @Override
    public Boolean saveWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO) {

        if (workOrderRemarkVO.getCreatorId() == null) {
            throw new BizCustomException(100,"创建人ID不能为空");
        }
        if (workOrderRemarkVO.getWorkOrderId() == null) {
            throw new BizCustomException(101,"工单ID不能为空");
        }
        if (workOrderRemarkVO.getRecipient() == null) {
            throw new BizCustomException(100,"收件人不能为空");
        }
        if (workOrderRemarkVO.getContent() == null || workOrderRemarkVO.getContent().trim().isEmpty()) {
            throw new BizCustomException(100,"内容不能为空");
        }
        if (workOrderRemarkVO.getStatus() == null) {
            throw new BizCustomException(100,"状态不能为空");
        }
        WorkOrderRemarks workOrderRemarks = new WorkOrderRemarks();
        BeanUtils.copyProperties(workOrderRemarkVO,workOrderRemarks);
        workOrderRemarks.setCreated(LocalDateTime.now());
        workOrderRemarks.setUpdated(LocalDateTime.now());
        return workOrderRemarksRepository.save(workOrderRemarks);
    }

    /**
     * 修改工单备注
     * @param workOrderRemarkVO
     * @return
     */
    @Override
    public Boolean updateWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO) {

        if (workOrderRemarkVO.getContent() == null || workOrderRemarkVO.getContent().trim().isEmpty()) {
            throw new BizCustomException(100,"内容不能为空");
        }
        LambdaQueryWrapper<WorkOrderRemarks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderRemarks::getId, workOrderRemarkVO.getId());
        WorkOrderRemarks workOrderRemarks =new WorkOrderRemarks();
        BeanUtils.copyProperties(workOrderRemarkVO,workOrderRemarks);
        workOrderRemarks.setUpdated(LocalDateTime.now());
        return workOrderRemarksRepository.update(workOrderRemarks, queryWrapper);
    }

    /**
     * 工单备注列表查询
     * @param workOrderRemarkVO
     * @return
     */
    @Override
    public List<WorkOrderRemarkDTO> listWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO) {
        LambdaQueryWrapper<WorkOrderRemarks> queryWrapper = new LambdaQueryWrapper<>();
        if(workOrderRemarkVO.getWorkOrderId() != null)
        {
            queryWrapper.eq(WorkOrderRemarks::getWorkOrderId, workOrderRemarkVO.getWorkOrderId());
        }
        if(workOrderRemarkVO.getCreatorId() != null)
        {
            queryWrapper.eq(WorkOrderRemarks::getCreatorId, workOrderRemarkVO.getCreatorId());
        }
        if(workOrderRemarkVO.getRecipient() != null)
        {
            queryWrapper.eq(WorkOrderRemarks::getRecipient, workOrderRemarkVO.getRecipient());
        }
        
        List<WorkOrderRemarks> workOrderRemarksList = workOrderRemarksRepository.list(queryWrapper);
        List<WorkOrderRemarkDTO> result = new ArrayList<>();
        if(workOrderRemarksList != null && !workOrderRemarksList.isEmpty())
            result= workOrderRemarksList.stream().map(workOrderRemarks -> {
                WorkOrderRemarkDTO workOrderRemarkDTO = new WorkOrderRemarkDTO();
                BeanUtils.copyProperties(workOrderRemarks, workOrderRemarkDTO);
                return workOrderRemarkDTO;
            }).toList();
        return result;
          
    }

    /**
     * 删除工单备注
     * @param id
     * @return
     */
    @Override
    public Boolean deleteWorkOrderRemark(Long id) {

        LambdaQueryWrapper<WorkOrderRemarks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderRemarks::getId, id);
        WorkOrderRemarks workOrderRemarks = workOrderRemarksRepository.getBaseMapper().selectById( id);
        workOrderRemarks.setDeleted(1);

        return workOrderRemarksRepository.update(workOrderRemarks, queryWrapper);
    }


}
