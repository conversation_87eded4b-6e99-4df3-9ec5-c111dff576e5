package com.neo.nova.app.service;

import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.Metric;

import java.util.List;
import java.util.Map;

/**
 * 客户指标关系服务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface CustomerMetricService {

    /**
     * 根据指标代码、指标ID和客户ID列表，判断客户是否属于该指标类型
     *
     * @param metricCode   指标代码
     * @param metricCodeId 指标ID
     * @param customerIds  客户ID列表
     * @return Map<Long, Boolean> 客户ID -> 是否属于该指标类型的映射
     */
    Map<Long, Boolean> checkCustomerMetricCodeId(Long tenantId, String metricCode, String metricCodeId, List<Long> customerIds);

    /**
     * 根据指标代码和客户ID列表，查询这批客户的指标ID
     *
     * @param metricCode  指标代码
     * @param customerIds 客户ID列表
     * @return Map<Long, Metric> 客户ID -> 指标映射
     */
    Map<Long, Metric> getCustomerMetricCodeIds(Long tenantId, String metricCode, List<Long> customerIds);


    /**
     * 根据指标代码和客户ID列表，查询这批客户的指标ID
     *
     * @param metricCode    指标代码
     * @param customerInfos 客户列表
     * @return Map<Long, Metric> 客户ID -> 指标映射
     */
    Map<Long, Metric> listCustomerMetricCodeIds(Long tenantId, String metricCode, List<CustomerInfo> customerInfos);


    /**
     * 根据指标代码和客户ID列表，查询这批客户的指标ID
     *
     * @param tenantId
     * @param metricCode
     * @param customerInfo
     * @return
     */
    Metric queryCustomerMetricCodeId(Long tenantId, String metricCode, CustomerInfo customerInfo);

    /**
     * 根据业务员ID列表，查询业务员对应的客户信息
     *
     * @param tenantId
     * @param salesIds
     * @return
     */
    Map<Long, CustomerInfo> queryMapBySalesIds(Long tenantId, List<Long> salesIds);
}
