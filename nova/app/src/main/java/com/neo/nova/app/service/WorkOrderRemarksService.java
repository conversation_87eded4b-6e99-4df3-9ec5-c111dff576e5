package com.neo.nova.app.service;

import com.neo.nova.app.vo.WorkOrderRemarkVO;
import com.neo.nova.domain.dto.WorkOrderRemarkDTO;

import java.util.List;

public interface WorkOrderRemarksService {
    Boolean saveWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO);

    Boolean updateWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO);

    List<WorkOrderRemarkDTO> listWorkOrderRemark(WorkOrderRemarkVO workOrderRemarkVO);

    Boolean deleteWorkOrderRemark(Long id);
}
