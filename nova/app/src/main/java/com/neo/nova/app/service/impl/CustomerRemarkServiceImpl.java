package com.neo.nova.app.service.impl;

import com.neo.api.Response;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.domain.dto.CustomerRemarkDTO;
import com.neo.nova.domain.entity.CustomerRemark;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.enums.CustomerRemarkTypeEnum;
import com.neo.nova.domain.gateway.CustomerRemarkRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class CustomerRemarkServiceImpl implements CustomerRemarkService {
    @Autowired
    private CustomerRemarkRepository customerRemarkRepository;

    @Override
    public Response addCustomerRemark(CustomerRemarkDTO customerRemarkDTO) {


        if(customerRemarkDTO.getContent()==null)
        {
            throw new BizCustomException(400, "内容不能为空");
        }
        if(customerRemarkDTO.getCustomerId()==null)
        {
            throw new BizCustomException(400, "客户id不能为空");
        }
        if(customerRemarkDTO.getType()==null)
        {
            throw new BizCustomException(400, "type不能为空");
        }

        CustomerRemark customerRemark = new CustomerRemark();

        BeanUtils.copyProperties(customerRemarkDTO, customerRemark);
        customerRemark.setCreateTime(LocalDateTime.now());
        customerRemark.setUpdateTime(LocalDateTime.now());
        customerRemarkRepository.save(customerRemark);
        return Response.buildSuccess();

    }
    @Override
    public Boolean addCustomerRemarkByAction(WorkOrderDetail workOrderDetail)
    {
        CustomerRemark customerRemark = new CustomerRemark();
        customerRemark.setCustomerId(Long.valueOf(workOrderDetail.getBizId()));
        customerRemark.setFollowUpTime(workOrderDetail.getCompleteTime());
        customerRemark.setCreateTime(LocalDateTime.now());
        customerRemark.setType(CustomerRemarkTypeEnum.getByCode(workOrderDetail.getAction()).getId());
        customerRemark.setContent("关联一条"+ ActionRelationEnums.getDescByActionName(workOrderDetail.getAction())+"记录,");
        customerRemark.setRecordId(workOrderDetail.getId());
       return true;

    }
}
