package com.neo.nova.app.service;

import com.neo.nova.app.vo.SalesDataUpdateRequest;

import java.util.List;

/**
 * 销售数据更新服务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SalesDataUpdateService {

    /**
     * 更新销售数据统计
     * 从订单主表和订单明细表查询近一个月的订单商品销售情况，
     * 汇总每天和每月单品/单店的销量，更新统计表
     *
     * @return 更新结果
     */
    boolean updateSalesStatistics();


    /**
     * 根据请求参数更新销售数据统计
     * 支持按用户ID、客户ID、时间范围等条件进行过滤更新
     *
     * @param request 更新请求参数
     * @return 更新结果
     */
    boolean updateSalesStatistics(SalesDataUpdateRequest request);
}
