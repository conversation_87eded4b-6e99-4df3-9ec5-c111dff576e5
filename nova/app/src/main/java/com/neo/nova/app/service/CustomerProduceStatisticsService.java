package com.neo.nova.app.service;

import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.entity.st_trd_customer_produce_month_info;

import java.util.List;

/**
 * 客户产品统计数据服务
 * 负责处理 st_trd_customer_produce_day_info 和 st_trd_customer_produce_month_info 表的新增/修改操作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface CustomerProduceStatisticsService {

    /**
     * 保存日统计数据
     * 不存在就新增，存在就修改（仅在数据有变化时更新）
     *
     * @param dayInfoList 日统计数据列表
     * @param tenantId    租户ID
     */
    void saveDayInfoRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId);

    /**
     * 保存月统计数据
     * 不存在就新增，存在就修改（仅在数据有变化时更新）
     *
     * @param monthInfoList 月统计数据列表
     * @param tenantId      租户ID
     * @param fromDay       日维度上报
     */
    void saveMonthInfoRecords(List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId, boolean fromDay);

    /**
     * 上报单品更新数据到月表
     *
     * @param dayInfoList 日统计数据列表
     * @param tenantId    租户ID
     */
    void aggregateDayToMonthRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId);
}
