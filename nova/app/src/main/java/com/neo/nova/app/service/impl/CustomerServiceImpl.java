package com.neo.nova.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.neo.api.MultiResponse;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.app.util.CustomerInfoEnrichmentUtil;
import com.neo.nova.app.vo.CustomerDetailVo;
import com.neo.nova.app.vo.CustomerQueryVO;
import com.neo.nova.app.vo.CustomerRemarkVO;
import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.dto.CustomerInfoDTO;
import com.neo.nova.domain.dto.CustomerRemarkDTO;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.CustomerRemark;
import com.neo.nova.domain.enums.CustomerLevelEnum;
import com.neo.nova.domain.enums.CustomerRemarkTypeEnum;
import com.neo.nova.domain.enums.CustomerStatusEnum;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.domain.gateway.CustomerRemarkRepository;
import com.neo.session.SessionContextHolder;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.user.client.tenant.api.MobileTenantService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class CustomerServiceImpl implements CustomerService {
    @Autowired
    private CustomerInfoRepository customerInfoRepository;

    @Autowired
    private CustomerInfoEnrichmentUtil customerInfoEnrichmentUtil;
    @Autowired
    private CustomerRemarkService customerRemarkService;


    @Autowired
    private MobileTenantService mobileTenantService;
    @Autowired
    private CustomerRemarkRepository customerRemarkRepository;
    @Autowired
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    /**
     * 查询客户列表
     *
     * @param customerQueryVO
     * @return
     */
    @Override
    public CustomerInfoDTO list(CustomerQueryVO customerQueryVO) {
        LambdaQueryWrapper<CustomerInfo> wrapper = bulidWrapper(customerQueryVO);
        wrapper.eq(CustomerInfo::getTenantId, customerQueryVO.getTenantId());
        wrapper.eq(CustomerInfo::getIsDeleted, 0);
        IPage<CustomerInfo> page = new Page<>(customerQueryVO.getPageIndex(), customerQueryVO.getPageSize());
        List<CustomerInfo> customerInfos = customerInfoRepository.list(page, wrapper);

        List<CustomerDTO> result = customerInfos.stream().map(customerInfo -> {
            CustomerDTO dto = new CustomerDTO();
            BeanUtils.copyProperties(customerInfo, dto);
            dto.setLevelName(CustomerLevelEnum.getById(customerInfo.getLevel()).getDesc());
            dto.setChannelName(MetricCodeEnum.getNameByCode(customerInfo.getChannel()));
            dto.setOpenDate(new SimpleDateFormat("yyyy-MM-dd").format(customerInfo.getOpenDate() * 1000));
            return dto;
        }).toList();

        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        customerInfoDTO.setCustomerInfos(customerInfoEnrichmentUtil.enrichCustomerDTOBatch(result));
        customerInfoDTO.setTotalCount(page.getTotal());
        return customerInfoDTO;
    }

    /**
     * 我的客户列表
     *
     * @param customerQueryVO
     * @return
     */
    @Override
    public CustomerInfoDTO myList(CustomerQueryVO customerQueryVO) {
        long userId = SessionContextHolder.getUserId();
        LambdaQueryWrapper<CustomerInfo> wrapper = bulidWrapper(customerQueryVO);
        wrapper.eq(CustomerInfo::getSalesId, userId);
        wrapper.eq(CustomerInfo::getIsDeleted, 0);
        IPage<CustomerInfo> page = new Page<>(customerQueryVO.getPageIndex(), customerQueryVO.getPageSize());
        List<CustomerInfo> customerInfos = customerInfoRepository.list(page, wrapper);
        // 使用工具类填充空字段
        List<CustomerDTO> result = customerInfos.stream().map(customerInfo -> {
            CustomerDTO dto = new CustomerDTO();
            dto.setLevelName(CustomerLevelEnum.getById(customerInfo.getLevel()).getDesc());
            dto.setChannelName(MetricCodeEnum.getNameByCode(customerInfo.getChannel()));
            dto.setOpenDate(new SimpleDateFormat("yyyy-MM-dd").format(customerInfo.getOpenDate() * 1000));
            BeanUtils.copyProperties(customerInfo, dto);
            return dto;
        }).toList();
        List<CustomerDTO> resuletList = customerInfoEnrichmentUtil.enrichCustomerDTOBatch(result);
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        customerInfoDTO.setCustomerInfos(resuletList);
        customerInfoDTO.setTotalCount(page.getTotal());
        return customerInfoDTO;
    }

    public LambdaQueryWrapper<CustomerInfo> bulidWrapper(CustomerQueryVO customerQueryVO) {
        LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<CustomerInfo>();
        if (customerQueryVO.getCustomerName() != null) {
            wrapper.and(w ->
                    w.like(CustomerInfo::getName, customerQueryVO.getCustomerName()).or().like(CustomerInfo::getMnemoCode, customerQueryVO.getCustomerName()));
        }
        if (customerQueryVO.getCustomerTypeIds() != null && !customerQueryVO.getCustomerTypeIds().isEmpty()) {
            wrapper.in(CustomerInfo::getCustomerTypeId, customerQueryVO.getCustomerTypeIds());
        }
        if (customerQueryVO.getCustomerLevelIds() != null && !customerQueryVO.getCustomerLevelIds().isEmpty()) {
            wrapper.in(CustomerInfo::getLevel, customerQueryVO.getCustomerLevelIds());
        }
        if (customerQueryVO.getStatus() != null) {
            wrapper.in(CustomerInfo::getStatus, customerQueryVO.getStatus());
        }
        if (customerQueryVO.getCustomerAreaId() != null) {
            wrapper.in(CustomerInfo::getCustomerAreaId, customerQueryVO.getCustomerAreaId());
        }
        if (customerQueryVO.getCustomerLineIds() != null) {
            wrapper.in(CustomerInfo::getCustomerLineId, customerQueryVO.getCustomerLineIds());
        }
        if (customerQueryVO.getSalesId() != null) {
            wrapper.in(CustomerInfo::getSalesId, customerQueryVO.getSalesId());
        }
        if (customerQueryVO.getDeliveryAddress() != null) {
            wrapper.like(CustomerInfo::getDeliveryAddress, customerQueryVO.getDeliveryAddress());
        }
        return wrapper;
    }

    /**
     * 公海客户查询
     *
     * @param customerQueryVO
     * @return
     */
    @Override
    public CustomerInfoDTO highSeasList(CustomerQueryVO customerQueryVO) {
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        LambdaQueryWrapper<CustomerInfo> wrapper = bulidWrapper(customerQueryVO);
        wrapper.eq(CustomerInfo::getStatus, CustomerStatusEnum.NO_FOLLOWUP.getCode());
        wrapper.eq(CustomerInfo::getIsDeleted, 0);
        IPage<CustomerInfo> page = new Page<>(customerQueryVO.getPageIndex(), customerQueryVO.getPageSize());
        List<CustomerInfo> customerInfos = customerInfoRepository.list(page, wrapper);

        // 使用工具类填充空字段

        if (!customerInfos.isEmpty()) {
            List<CustomerDTO> result = customerInfos.stream().map(customerInfo -> {
                CustomerDTO dto = new CustomerDTO();
                BeanUtils.copyProperties(customerInfo, dto);
                dto.setLevelName(CustomerLevelEnum.getById(customerInfo.getLevel()).getDesc());
                dto.setChannelName(MetricCodeEnum.getNameByCode(customerInfo.getChannel()));
                dto.setOpenDate(new SimpleDateFormat("yyyy-MM-dd").format(customerInfo.getOpenDate() * 1000));
                return dto;
            }).toList();


            customerInfoDTO.setCustomerInfos(customerInfoEnrichmentUtil.enrichCustomerDTOBatch(result));
            customerInfoDTO.setTotalCount(page.getTotal());
        }
        return customerInfoDTO;
    }

    @Override
    public List<CustomerInfo> search(CustomerQueryVO queryVO) {
        if (queryVO == null || queryVO.getTenantId() == null) {
            return null;
        }

        // 1. 先直接匹配系统中的客户名称
        LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerInfo::getTenantId, queryVO.getTenantId())
                .eq(CustomerInfo::getIsDeleted, 0);
        if (StringUtils.isNotBlank(queryVO.getCustomerName())) {
            wrapper.and(wra -> wra.eq(CustomerInfo::getName, queryVO.getCustomerName()));
        }
        if (CollectionUtils.isNotEmpty(queryVO.getCustomerNames())) {
            wrapper.and(wra -> wra.in(CustomerInfo::getName, queryVO.getCustomerNames()));
        }
        return customerInfoRepository.list(wrapper);
    }

    @Override
    public CustomerInfo getById(Long id) {
        return customerInfoRepository.getById(id);
    }

    @Override
    public List<CustomerInfo> listByIds(Collection<Long> ids) {
        LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CustomerInfo::getId, ids)
                .eq(CustomerInfo::getIsDeleted, 0);
        return customerInfoRepository.list(wrapper);
    }

    @Override
    public CustomerDetailVo getCustomerDetail(Long customerId) {
        CustomerDetailVo customerDetailVo = new CustomerDetailVo();
        CustomerInfo customerInfo = customerInfoRepository.getById(customerId);
        if (customerInfo != null) {
            CustomerDTO customerDTO = new CustomerDTO();
            BeanUtils.copyProperties(customerInfo, customerDTO);
            customerDTO.setLevelName(CustomerLevelEnum.getById(customerInfo.getLevel()).getDesc());
            customerDTO.setChannelName(MetricCodeEnum.getNameByCode(customerInfo.getChannel()));
            customerDTO.setOpenDate(new SimpleDateFormat("yyyy-MM-dd").format(customerInfo.getOpenDate() * 1000));
            customerDTO.setSaleNumber(mobileTenantService.queryMobileInfoByUserId(customerDTO.getSalesId(), customerDTO.getTenantId()).getData().getMobile());
            BeanUtils.copyProperties(customerInfoEnrichmentUtil.enrichCustomerDTO(customerDTO), customerDetailVo);
            List<CustomerRemark> customerRemarkList = customerRemarkRepository.list(new LambdaQueryWrapper<CustomerRemark>()
                    .eq(CustomerRemark::getCustomerId, customerId)
                    .eq(CustomerRemark::getIsDeleted, 0));
            List<CustomerRemark> customerRemarkVOStationList = customerRemarkList.stream().filter(customerRemark -> customerRemark.getType() == CustomerRemarkTypeEnum.STATION.getId()).toList();
            List<CustomerRemark> customerRemarks = customerRemarkList.stream().filter(customerRemark -> customerRemark.getType() != CustomerRemarkTypeEnum.STATION.getId()).toList();
            List<CustomerRemark> sortedList = new ArrayList<>(customerRemarkVOStationList);
            sortedList.addAll(customerRemarks);
            List<CustomerRemarkVO> customerRemarkVOS = sortedList.stream().map(customerRemark -> {
                CustomerRemarkVO customerRemarkVO = new CustomerRemarkVO();
                BeanUtils.copyProperties(customerRemark, customerRemarkVO);

// 定义时间格式器
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
// 将 LocalDateTime 格式化为字符串
                String formattedCreateTime = customerRemark.getFollowUpTime().format(formatter);

// 设置格式化后的时间到 customerRemarkVO 对象中
                customerRemarkVO.setFollowUpTime(formattedCreateTime);
                return customerRemarkVO;
            }).toList();
            customerDetailVo.setCustomerRemarkVOList(customerRemarkVOS);

            String priceName = getPriceName(customerDetailVo.getPriceTypeId());
            customerDetailVo.setPriceName(priceName);
        } else {
            throw new BizCustomException(400, "客户不存在");
        }

        return customerDetailVo;
    }

    private String getPriceName(Long priceTypeId) {

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDeleted(true);
        option.setIncludeDisable(true);

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(21);
        MultiResponse<TagLeafInfoDto> response = businessTreeTagReadReadService.queryPricePlanSetting(param, option);

        if (response.isSuccess() && response.getData() != null) {
            Map<Long, TagLeafInfoDto> tagLeafInfoDtoMap = response.getData().stream()
                    .collect(Collectors.toMap(
                            TagLeafInfoDto::getId,
                            tag -> tag,
                            (existing, replacement) -> existing
                    ));
            return tagLeafInfoDtoMap.get(priceTypeId).getName();
        }

        return "";

    }


    @Override
    public Boolean addAndUpdateCustomer(CustomerDTO customerDTO) {
        // 入参校验
        if (customerDTO == null) {
            throw new BizCustomException(400, "客户信息不能为空");
        }
        if (StringUtils.isBlank(customerDTO.getName())) {
            throw new BizCustomException(400, "客户名称不能为空");
        }
        if (StringUtils.isBlank(customerDTO.getChannel())) {
            throw new BizCustomException(400, "渠道不能为空");
        }
        if (customerDTO.getAdminRegionId() == null) {
            throw new BizCustomException(400, "行政区域不能为空");
        }
        if (StringUtils.isBlank(customerDTO.getLinkMan())) {
            throw new BizCustomException(400, "联系人不能为空");
        }
        if (customerDTO.getStatus() == CustomerStatusEnum.ALREADY_FOLLOWUP.getCode()) {
            if (customerDTO.getSalesId() == null) {
                throw new BizCustomException(400, "跟进状态时业务员不能为空");
            }
        }
        CustomerInfo customerInfo = new CustomerInfo();
        if (customerDTO.getId() != null) {
            // 更新操作
            customerInfo = customerInfoRepository.getById(customerDTO.getId());
            if (customerInfo == null) {
                throw new BizCustomException(400, "客户id不存在");

            }
            // 设置创建/更新信息
            long currentUserId = SessionContextHolder.getUserId();
            long currentTime = System.currentTimeMillis() / 1000;
            // 更新操作信息
            customerInfo.setUpdatedBy(currentUserId);
            customerInfo.setUpdated(currentTime);

            CustomerRemark customerRemark = new CustomerRemark();
            customerRemark.setCustomerId(customerInfo.getId());
            customerRemark.setType(CustomerRemarkTypeEnum.STATION.getId());
            customerRemark.setFollowUpTime(LocalDateTime.now());
            customerRemark.setCreateTime(LocalDateTime.now());
            customerRemark.setContent(customerDTO.getContent());
            customerRemark.setCreatorId(currentUserId);
            customerRemarkRepository.save(customerRemark);
            customerInfoRepository.updateById(customerInfo);
            return true;
        }

        // 复制属性
        BeanUtils.copyProperties(customerDTO, customerInfo);
        customerInfo.setSalesId(customerDTO.getSalesId());

        if (customerInfo.getStatus() == null) {
            // 设置公海客户标识，salesId为0
            customerInfo.setStatus(CustomerStatusEnum.NO_FOLLOWUP.getCode());
        }

        // 设置租户ID
        if (customerInfo.getTenantId() == null) {
            customerInfo.setTenantId(SessionContextHolder.getTenantId());
        }

        // 设置创建/更新信息
        long currentUserId = SessionContextHolder.getUserId();
        long currentTime = System.currentTimeMillis();

        if (customerInfo.getId() == null) {
            // 新增操作
            customerInfo.setCreatedBy(currentUserId);
            customerInfo.setCreated(currentTime);
        }

        // 更新操作信息
        customerInfo.setUpdatedBy(currentUserId);
        customerInfo.setUpdated(currentTime);
        customerInfo.setIsDeleted(0);


        CustomerRemarkDTO customerRemark = new CustomerRemarkDTO();
        customerRemark.setCustomerId(customerInfo.getId());
        customerRemark.setCreatorId(currentUserId);
        customerRemark.setType(CustomerRemarkTypeEnum.STATION.getId());
        customerRemark.setContent(customerDTO.getContent());
        customerRemarkService.addCustomerRemark(customerRemark);
        // 保存客户信息
        return customerInfoRepository.save(customerInfo);
    }
}
