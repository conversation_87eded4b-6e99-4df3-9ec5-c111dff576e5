package com.neo.nova.app.sync.executor;

import com.neo.nova.app.sync.TableSyncConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * 分页同步任务
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Slf4j
//@Component
public class PageSyncTask<T, R> implements Callable<PageSyncResult> {
 

    private final TableSyncConfig<T, R> config;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final int offset;
    private final int limit;
    private final int pageNum;


    public PageSyncTask(TableSyncConfig<T, R> config, LocalDateTime startTime, LocalDateTime endTime,
                        int offset, int limit, int pageNum) {
        this.config = config;
        this.startTime = startTime;
        this.endTime = endTime;
        this.offset = offset;
        this.limit = limit;
        this.pageNum = pageNum;
    }

    @Override
    public PageSyncResult call() throws Exception {
        String tableName = config.getSqlServerTableName();
        log.info("开始同步表{}第{}页数据，偏移量：{}，限制：{}", tableName, pageNum, offset, limit);

        try {
            // 1. 分页查询SQL Server数据
            List<T> updatedData = config.queryUpdatedDataFromSqlServerWithPagination(startTime, endTime, offset, limit);

            if (CollectionUtils.isEmpty(updatedData)) {
                log.debug("表{}第{}页没有数据", tableName, pageNum);
                return new PageSyncResult(pageNum, 0, 0, 0, true, null);
            }

            log.debug("表{}第{}页查询到{}条数据", tableName, pageNum, updatedData.size());

            // 2. 获取主键列表
            List<Object> primaryKeys = updatedData.stream()
                    .map(config::getSqlServerPrimaryKeyValue)
                    .toList();

            // 3. 从MySQL查询现有数据
            Map<Object, R> existingDataMap = config.queryExistingDataFromMysql(primaryKeys);

            // 4. 分类数据：新增和更新
            List<R> insertList = new ArrayList<>();
            List<R> updateList = new ArrayList<>();

            for (T data : updatedData) {

                Object primaryKey = config.getSqlServerPrimaryKeyValue(data);
                R existingData = existingDataMap.get(primaryKey.toString());

                if (existingData == null) {
                    // 新数据，需要插入
                    insertList.add(config.convertToMysqlEntity(data));
                } else if (config.needUpdate(data, existingData)) {
                    // 现有数据，需要更新
                    updateList.add(config.convertToMysqlEntity(data));
                }
            }

            // 5. 执行批量操作
            int insertCount = 0;
            int updateCount = 0;

            if (!insertList.isEmpty()) {
                log.debug("表{}第{}页批量插入{}条数据", tableName, pageNum, insertList.size());
                config.batchInsertToMysql(insertList);
                insertCount = insertList.size();
            }

            if (!updateList.isEmpty()) {
                log.debug("表{}第{}页批量更新{}条数据", tableName, pageNum, updateList.size());
                config.batchUpdateToMysql(updateList);
                updateCount = updateList.size();


            }


            try {
                config.after();
            } catch (Exception e) {
                log.error("同步后续任务异常", e);
            }

            log.info("表{}第{}页同步完成，处理{}条，插入{}条，更新{}条",
                    tableName, pageNum, updatedData.size(), insertCount, updateCount);

            return new PageSyncResult(pageNum, updatedData.size(), insertCount, updateCount, true, null);

        } catch (Exception e) {
            log.error("表{}第{}页同步失败: {}", tableName, pageNum, e.getMessage(), e);
            return new PageSyncResult(pageNum, 0, 0, 0, false, e.getMessage());
        }
    }
}
