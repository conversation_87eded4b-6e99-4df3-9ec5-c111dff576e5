package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustarea;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.client.param.TreeTagSaveParam;
import com.neo.tagcenter.client.rpc.TreeTagWriteService;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamCustareaConfigImpl<T, R> extends AbstractTagLeafInfoTableSyncConfig<SynSlPamCustarea, TagLeafInfo> {

    @Resource
    private TagLeafInfoMapper tagLeafInfoMapper;

    @Resource
    private TreeTagWriteService treeTagWriteService;

    @Resource
    private TagSyncService tagSyncService;

    @Override
    protected BaseMapper<TagLeafInfo> getMysqlMapper() {
        return tagLeafInfoMapper;
    }

    @Override
    public void batchUpdateToMysql(List<TagLeafInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.debug("批量更新MySQL表{}，数据量：{}", getMysqlTableName(), dataList.size());

        for (TagLeafInfo data : dataList) {
            QueryWrapper<TagLeafInfo> objectQueryWrapper = new QueryWrapper<>();
            SqlServerSourceEnums sqlserverSource = getSqlserverSource();
            switch (sqlserverSource) {
                case SQLSERVER1:
                    objectQueryWrapper.eq("outId1", data.getOutId1());
                    break;
                case SQLSERVER2:
                    objectQueryWrapper.eq("outId2", data.getOutId2());
                    break;
            }
            getMysqlMapper().update(data, objectQueryWrapper);
        }
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custarea";
    }

    @Override
    public String getMysqlTableName() {
        return "TagLeafInfo";
    }

    @Override
    public QueryWrapper<SynSlPamCustarea> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustarea> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("Depth").orderByAsc("ParentID");

        return queryWrapper;
    }

    @Override
    public boolean needUpdate(SynSlPamCustarea sqlServerData, TagLeafInfo mysqlData) {
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较客户区域编号
        if (!Objects.equals(sqlServerData.getCustareano(), mysqlData.getCode())) {
            return true;
        }

        // 比较客户区域名称
        if (!Objects.equals(sqlServerData.getCustareaname(), mysqlData.getName())) {
            return true;
        }

        // 比较描述
        if (!Objects.equals(sqlServerData.getDescription(), mysqlData.getDescription())) {
            return true;
        }

        // 比较状态
        if (!Objects.equals(sqlServerData.getUstate(), mysqlData.getIsEnabled() != null && mysqlData.getIsEnabled() == 0 ? "1" : "0")) {
            return true;
        }

        // 比较层级深度
        if (!Objects.equals(sqlServerData.getDepth(), mysqlData.getLevel())) {
            return true;
        }

        // 比较父级ID
        if (!Objects.equals(sqlServerData.getParentid() != null ? sqlServerData.getParentid().toString() : null,
                mysqlData.getOutParentId1())) {
            return true;
        }

        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(TagLeafInfo data) {
        SqlServerSourceEnums sqlserverSource = getSqlserverSource();
        switch (sqlserverSource) {
            case SQLSERVER1:
                return data.getOutId1();
            case SQLSERVER2:
                return data.getOutId2();
        }
        return data.getId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlPamCustarea data) {
        return data.getId();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamCustarea data) {
        return fromCustarea(data);
    }

    /**
     * SynSlPamCustarea转TagLeafInfo
     *
     * @param custarea
     * @return
     */
    public TagLeafInfo fromCustarea(SynSlPamCustarea custarea) {
        if (custarea == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        Long parentId = custarea.getParentid() != null ? custarea.getParentid().longValue() : null;

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(custarea.getId()));
                tagLeafInfo.setOutParentId1(String.valueOf(parentId));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(custarea.getId()));
                tagLeafInfo.setOutParentId2(String.valueOf(parentId));
                break;
        }

        tagLeafInfo.setName(custarea.getCustareaname());
        tagLeafInfo.setCode(custarea.getCustareano());
        tagLeafInfo.setDescription(custarea.getDescription());

        // 层级信息映射
        tagLeafInfo.setLevel(custarea.getDepth());
//        tagLeafInfo.setPos(custarea.getOrdercode());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(custarea.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

    @Override
    public void batchInsertToMysql(List<TagLeafInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.debug("批量插入MySQL表{}，数据量：{}", getMysqlTableName(), dataList.size());

        // 分批插入，避免单次插入数据量过大
        int batchSize = 100;
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<TagLeafInfo> batchData = dataList.subList(i, endIndex);


            for (TagLeafInfo data : batchData) {

                QueryWrapper<TagLeafInfo> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("name", data.getName());
                TagLeafInfo dbInfo = getMysqlMapper().selectOne(queryWrapper);
                if (dbInfo == null) {
                    QueryWrapper<TagLeafInfo> queryWrapper1 = new QueryWrapper<>();
                    switch (getSqlserverSource()) {
                        case SQLSERVER1:
                            queryWrapper1.eq("outId1", data.getOutParentId1());
                            break;
                        case SQLSERVER2:
                            queryWrapper1.eq("outId2", data.getOutParentId2());
                            break;
                    }
                    TagLeafInfo tagLeafInfo = getMysqlMapper().selectOne(queryWrapper1);
                    if (tagLeafInfo != null) {
                        data.setParentId(tagLeafInfo.getId());
                    }
                    TreeTagSaveParam treeTagSaveParam = tagSyncService.getTreeTagSaveParam(data, TagDomainEnums.SALES_REGION);
                    treeTagWriteService.addTagLeafInfo(treeTagSaveParam);
                } else {
                    switch (getSqlserverSource()) {
                        case SQLSERVER1:
                            data.setOutId1(data.getOutId1());
                            data.setOutParentId1(data.getOutParentId1());
                            break;
                        case SQLSERVER2:
                            data.setOutId2(data.getOutId2());
                            data.setOutParentId2(data.getOutParentId2());
                            break;
                    }
                    data.setId(dbInfo.getId());
                    getMysqlMapper().updateById(data);
                }
            }
        }
    }

    @Override
    public void after() {
        super.after();
    }
}
