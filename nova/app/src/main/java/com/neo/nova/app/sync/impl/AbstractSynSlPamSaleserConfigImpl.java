package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.sync.AbstractTagLeafInfoTableSyncConfig;
import com.neo.nova.domain.entity.sqlserver.SynSlPamSaleser;
import com.neo.tagcenter.app.constants.EnableStatus;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractSynSlPamSaleserConfigImpl<T, R> extends AbstractTagLeafInfoTableSyncConfig<SynSlPamSaleser, TagLeafInfo> {

    @Resource
    private TagLeafInfoMapper tagLeafInfoMapper;

    @Override
    protected BaseMapper<TagLeafInfo> getMysqlMapper() {
        return tagLeafInfoMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_saleser";
    }

    @Override
    public String getMysqlTableName() {
        return "TagLeafInfo";
    }

    @Override
    public void batchUpdateToMysql(List<TagLeafInfo> dataList) {
        super.batchUpdateToMysql(dataList);
    }

    @Override
    public QueryWrapper<SynSlPamSaleser> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamSaleser> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }

    @Override
    public boolean needUpdate(SynSlPamSaleser sqlServerData, TagLeafInfo mysqlData) {
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较销售员编号
        if (!Objects.equals(sqlServerData.getSaleserno(), mysqlData.getCode())) {
            return true;
        }

        // 比较销售员名称
        if (!Objects.equals(sqlServerData.getSalesername(), mysqlData.getName())) {
            return true;
        }

        // 比较状态
        if (!Objects.equals(sqlServerData.getUstate(), mysqlData.getIsEnabled() != null && mysqlData.getIsEnabled() == 0 ? "1" : "0")) {
            return true;
        }

        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(TagLeafInfo data) {
        SqlServerSourceEnums sqlserverSource = getSqlserverSource();
        switch (sqlserverSource) {
            case SQLSERVER1:
                return data.getOutId1();
            case SQLSERVER2:
                return data.getOutId2();
        }
        return data.getId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlPamSaleser data) {
        return data.getId();
    }

    @Override
    public TagLeafInfo convertToMysqlEntity(SynSlPamSaleser data) {
        return fromSaleser(data);
    }

    /**
     * SynSlPamSaleser转TagLeafInfo
     *
     * @param saleser
     * @return
     */
    public TagLeafInfo fromSaleser(SynSlPamSaleser saleser) {
        if (saleser == null) {
            return null;
        }

        TagLeafInfo tagLeafInfo = new TagLeafInfo();

        // 基本信息映射
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfo.setOutId1(String.valueOf(saleser.getId()));
                break;
            case SQLSERVER2:
                tagLeafInfo.setOutId2(String.valueOf(saleser.getId()));
                break;
        }

        tagLeafInfo.setName(saleser.getSalesername());
        tagLeafInfo.setCode(saleser.getSaleserno());

        // 状态映射
        tagLeafInfo.setIsEnabled("1".equals(saleser.getUstate()) ? EnableStatus.ENABLE.getValue() : EnableStatus.DISABLE.getValue());
        tagLeafInfo.setIsDeleted(0);

        return tagLeafInfo;
    }

    @Override
    public void after() {
        super.after();
    }
}
