package com.neo.nova.app.service;

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.vo.CustomerPurchasedGoodsVO;
import com.neo.nova.app.vo.OrderDetailQueryVO;
import com.neo.nova.domain.dto.OrderDetailDTO;

import java.util.List;

/**
 * 订单明细服务接口
 */
public interface OrderDetailService {

    /**
     * 创建订单明细
     *
     * @param orderDetailDTO 订单明细信息
     * @return 创建结果
     */
    SingleResponse<OrderDetailDTO> createOrderDetail(OrderDetailDTO orderDetailDTO);

    /**
     * 更新订单明细
     *
     * @param orderDetailDTO 订单明细信息
     * @return 更新结果
     */
    SingleResponse<Boolean> updateOrderDetail(OrderDetailDTO orderDetailDTO);

    /**
     * 删除订单明细
     *
     * @param id 订单明细ID
     * @return 删除结果
     */
    SingleResponse<Boolean> deleteOrderDetail(Integer id);

    /**
     * 获取订单明细详情
     *
     * @param id 订单明细ID
     * @return 订单明细详情
     */
    SingleResponse<OrderDetailDTO> getOrderDetailById(Integer id);

    /**
     * 根据订单ID查询订单明细列表
     *
     * @param orderId 订单ID
     * @return 订单明细列表
     */
    SingleResponse<List<OrderDetailDTO>> getOrderDetailsByOrderId(Long orderId);

    /**
     * 根据客户ID查询订单明细列表
     *
     * @param customerId 客户ID
     * @return 订单明细列表
     */
    SingleResponse<List<OrderDetailDTO>> getOrderDetailsByCustomerId(Long customerId);

    /**
     * 批量删除订单明细
     *
     * @param ids 订单明细ID列表
     * @return 删除结果
     */
    SingleResponse<Boolean> batchDeleteOrderDetails(List<Integer> ids);

    /**
     * 分页查询订单明细列表
     * 支持多种查询条件：订单ID、客户ID、商品ID、价格范围、时间范围等
     *
     * @param queryVO 查询条件
     * @return 分页查询结果
     */
    PageResponse<OrderDetailDTO> pageOrderDetails(OrderDetailQueryVO queryVO);

    /**
     * 根据客户ID查询客户购买过的所有商品信息
     * 包含商品基本信息、购买统计信息等
     *
     * @param customerId 客户ID
     * @param includeDetails 是否包含购买详情
     * @return 客户购买的商品列表
     */
    SingleResponse<List<CustomerPurchasedGoodsVO>> getCustomerPurchasedGoods(Long customerId, Boolean includeDetails);
}
