package com.neo.nova.app.service;

import com.neo.nova.app.request.ComplexMetricListRequest;
import com.neo.nova.domain.dto.MetricListDTO;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;

import java.util.Map;
import java.util.Set;

public interface MetricService {
    MetricListDTO list(ComplexMetricListRequest complexMetricListRequest);


    void init(Long tenantId);

    /**
     * 查询指标数据
     *
     * @return
     */
    Map<Long, TagLeafInfoDto> queryTagInfo(Long tenantId, String metricCode, Set<Long> ids);


    /**
     * 查询指标数据
     */
    Map<Long, String> queryNames(Long tenantId, String metricCode, Set<Long> ids);


}
