package com.neo.nova.app.vo;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;

/**
 * 饼图数据VO
 * 包含年度和月度的目标、实际值、完成率等信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PieChartVO {

    /**
     * 年度完成率（实际值/目标值）
     */
    private BigDecimal yearPercent;

    /**
     * 月度完成率（实际值/目标值）
     */
    private BigDecimal monthPercent;

    /**
     * 年度时间进度（已过时间/全年时间）
     */
    private BigDecimal yearTimePercent;

    /**
     * 月度时间进度（已过天数/当月总天数）
     */
    private BigDecimal monthTimePercent;

    /**
     * 年度目标值
     */
    private BigDecimal yearTargetValue;

    /**
     * 月度目标值
     */
    private BigDecimal monthTargetValue;

    /**
     * 年度实际值
     */
    private BigDecimal yearActualValue;

    /**
     * 月度实际值
     */
    private BigDecimal monthActualValue;

    /**
     * 年度目标完成状态
     */
    private String yearStatus;

    /**
     * 月度目标完成状态
     */
    private String monthStatus;

    /**
     * 年度剩余目标值
     */
    private BigDecimal yearRemainingTarget;

    /**
     * 月度剩余目标值
     */
    private BigDecimal monthRemainingTarget;

    /**
     * 计算并设置完成率（保留2位小数）
     */
    public void calculatePercents() {
        this.yearPercent = calculatePercent(yearActualValue, yearTargetValue);
        this.monthPercent = calculatePercent(monthActualValue, monthTargetValue);
    }

    /**
     * 计算并设置状态
     */
    public void calculateStatus() {
        this.yearStatus = getStatus(yearPercent, yearTimePercent);
        this.monthStatus = getStatus(monthPercent, monthTimePercent);
    }

    /**
     * 计算并设置剩余目标
     */
    public void calculateRemainingTargets() {
        this.yearRemainingTarget = calculateRemaining(yearTargetValue, yearActualValue);
        this.monthRemainingTarget = calculateRemaining(monthTargetValue, monthActualValue);
    }

    /**
     * 计算完成率
     */
    private BigDecimal calculatePercent(BigDecimal actual, BigDecimal target) {
        if (target == null || target.compareTo(BigDecimal.ZERO) <= 0 || actual == null) {
            return BigDecimal.ZERO;
        }
        return actual.divide(target, 4, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100));
    }

    /**
     * 获取完成状态
     */
    private String getStatus(BigDecimal percent, BigDecimal timePercent) {
        if (percent == null || timePercent == null) {
            return "未知";
        }

        BigDecimal percentValue = timePercent.divide(new BigDecimal(100), RoundingMode.HALF_DOWN);

        if (percentValue.compareTo(BigDecimal.ZERO) >= 1.0) {
            return "已完成";
        } else if (percentValue.compareTo(timePercent) >= 0) {
            return "进度正常";
        } else if (percentValue.compareTo(timePercent.plus(new MathContext("0.8"))) >= 0) {
            return "进度偏慢";
        } else {
            return "进度严重滞后";
        }
    }

    /**
     * 计算剩余目标
     */
    private BigDecimal calculateRemaining(BigDecimal target, BigDecimal actual) {
        if (target == null) {
            return BigDecimal.ZERO;
        }
        if (actual == null) {
            return target;
        }
        BigDecimal remaining = target.subtract(actual);
        return BigDecimal.ZERO.max(remaining);
    }
}
