package com.neo.nova.app.service;

import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.GoodsInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 数据匹配服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface DataMatchingService {


    /**
     * 智能匹配客户信息（先系统名称匹配，再别名匹配）
     *
     * @param customerNames 客户名称
     * @param tenantId      租户ID
     * @return 匹配的客户信息，如果没有匹配到返回null
     */
    Map<String, CustomerInfo> matchCustomerSmart(Collection<String> customerNames, Long tenantId);

    /**
     * 智能匹配货品信息
     * 不支持客户-别名精准搜索
     *
     * @param goodsNames 货品名称
     * @param tenantId   租户ID
     * @return 匹配的货品信息，如果没有匹配到返回null
     */
    Map<String, GoodsInfo> matchGoodsSmart(Collection<String> goodsNames,Long tenantId);

    /**
     * 保存用户级客户别名映射关系
     *
     * @param customerAlias  客户别名
     * @param customerId     客户ID
     * @param dataSourceType 数据源类型
     * @param tenantId       租户ID
     * @param userId         用户ID
     */
    void saveUserCustomerAliasMapping(String customerAlias, Long customerId, String dataSourceType, Long tenantId, Long userId);


    /**
     * 保存客户级货品别名映射关系
     *
     * @param goodsAlias     货品别名
     * @param goodsId        货品ID
     * @param customerId     客户ID
     * @param dataSourceType 数据源类型
     * @param tenantId       租户ID
     * @param userId         用户ID
     */
    void saveCustomerGoodsAliasMapping(String goodsAlias, Long goodsId, Long customerId, String dataSourceType, Long tenantId, Long userId);
}
