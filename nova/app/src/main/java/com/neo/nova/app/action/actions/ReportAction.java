package com.neo.nova.app.action.actions;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.CheckInModel;
import com.neo.nova.app.action.models.ReportModel;
import com.neo.nova.app.action.records.ReportRecord;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.neo.nova.app.action.enums.WorkOrderDetailStatusEnums.COMPLETED;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 汇报组件
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 19:22
 */
@Component
@Slf4j
public class ReportAction extends AbstractBaseAction {
    @Resource
    private IWorkOrderRepository workOrderRepository;
    @Resource
    private IWorkOrderDetailRepository workOrderDetailRepository;


    /**
     * 获取动作名称
     *
     * @return
     */
    @Override
    public String getActionName() {
        return "report";
    }

    /**
     * 参数验证
     *
     * @param actionBaseModel
     * @return
     */
    @Override
    public Boolean paramsVerification(ActionBaseModel actionBaseModel) {
        try {
            // 1. 基础参数校验
            if (actionBaseModel == null) {
                log.error("汇报参数验证失败: 参数对象为空");
                actionBaseModel = new ActionBaseModel();
                actionBaseModel.setExceptionMessage("参数对象不能为空");
                return false;
            }

            // 2. 参数类型转换校验
//            if (!(actionBaseModel instanceof ReportModel)) {
//                log.error("汇报参数验证失败: 参数类型错误, 期望ReportModel, 实际: {}",
//                        actionBaseModel.getClass().getSimpleName());
//                actionBaseModel.setExceptionMessage("参数类型错误，期望ReportModel类型");
//                return false;
//            }

            ActionBaseModel params =  actionBaseModel;

            // 3. 必填字段校验

            if (params.getWorkOrderId() == null) {
                log.error("汇报参数验证失败: 工单ID不能为空");
                params.setExceptionMessage("工单ID不能为空");
                return false;
            }

            // 4. 字段长度校验
            if (params.getExtraInfo() != null && String.valueOf(params.getExtraInfo().get("content")).length() > 2000) {
                log.error("汇报参数验证失败: 汇报内容长度超过限制, 当前长度: {}", params);
                params.setExceptionMessage("汇报内容长度不能超过2000个字符");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("汇报参数验证异常: params->{}", JSON.toJSONString(actionBaseModel), e);
            if (actionBaseModel != null) {
                actionBaseModel.setExceptionMessage("参数验证异常: " + e.getMessage());
            }
            return false;
        }
    }


    /**
     * 基础执行类
     *
     * @param actionBaseModel
     */
    @Override
    public boolean execute(ActionBaseModel actionBaseModel) {
        ActionBaseModel params = actionBaseModel;
        LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderDetail::getWorkOrderId, params.getWorkOrderId());
        WorkOrderDetail workOrderDetail = workOrderDetailRepository.getOne(queryWrapper);
        workOrderDetail.setStatus(COMPLETED.getCode());
        Map<String, Object> actionDetail = new HashMap<>();
        //不要丢了原来的东西
        if(StringUtils.hasText(workOrderDetail.getActionDetail())){
            actionDetail.putAll(JSONObject.parseObject(workOrderDetail.getActionDetail(), Map.class));
        }

        //添加新的
        if(MapUtils.isNotEmpty(actionBaseModel.getExtraInfo())){
            actionDetail.putAll(actionBaseModel.getExtraInfo());
        }

        workOrderDetail.setActionDetail(JSON.toJSONString(actionDetail));
        workOrderDetail.setCompleteTime(LocalDateTime.now());

        return workOrderDetailRepository.updateById(workOrderDetail);
    }

    /**
     * 创建任务
     *
     * @param actionTaskModel 任务参数
     */
    @Override
    public boolean createTask(ActionTaskModel actionTaskModel) {
        Long workOrderId = actionTaskModel.getWorkOrderId();
        if (workOrderId != null) {
            // 初始化工单
            WorkOrderDetail workOrderDetail = new WorkOrderDetail();
            workOrderDetail.setWorkOrderId(workOrderId);
            workOrderDetail.setTenantId(actionTaskModel.getTenantId());
            workOrderDetail.setAction(actionTaskModel.getActionName());
            workOrderDetail.setExecutorId(actionTaskModel.getUserId());
            workOrderDetail.setActionDetail(JSON.toJSONString(actionTaskModel.getActionDetail()));
            workOrderDetail.setType(actionTaskModel.getWorkOrderDetailType());
            workOrderDetail.setExecutorId(actionTaskModel.getUserId());
            workOrderDetail.setCreatorId(SessionContextHolder.getUserId());
            workOrderDetail.setSort(actionTaskModel.getSorted());
            workOrderDetail.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
            workOrderDetail.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
            workOrderDetail.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
            return workOrderDetailRepository.save(workOrderDetail);
        }
        return false;
    }

}
