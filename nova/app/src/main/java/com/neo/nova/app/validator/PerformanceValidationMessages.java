package com.neo.nova.app.validator;

import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.MetricDataTypeEnum;

/**
 * 业绩目标参数校验错误信息常量类
 *
 * <AUTHOR> Assistant
 * @since 2025/7/21
 */
public class PerformanceValidationMessages {

    // 基础参数校验错误信息
    public static final String TARGETS_EMPTY = "目标列表不能为空，请至少添加一个目标";
    public static final String TARGET_NAME_EMPTY = "目标名称不能为空，请为所有目标填写名称";
    public static final String TARGET_UNIT_EMPTY = "目标单位不能为空，请为所有目标填写单位";
    public static final String TARGET_VALUE_EMPTY = "目标值不能为空，请为所有目标填写目标值";
    public static final String TARGET_VALUE_INVALID = "目标值必须大于0，请检查目标值设置";
    public static final String TARGET_OWNER_INVALID = "责任人不能为空，请检查目标值设置";

    // 计划相关校验错误信息
    public static final String PLAN_INFO_MISSING = "未传入计划ID时，必须提供完整的计划信息（周期类型、开始时间、结束时间）";
    public static final String PERIOD_TYPE_INVALID = "无效的周期类型，支持的类型：年度(0)、半年(1)、季度(2)、双月(3)、月度(4)、自定义日(9)";
    public static final String PERIOD_START_INVALID = "计划开始时间不能晚于结束时间，请检查时间设置";
    public static final String PLAN_ID_NOT_EXISTS = "指定的计划ID不存在或已被删除，请检查计划ID";

    // 目标业务逻辑校验错误信息
    public static final String PARENT_ID_DUPLICATE = "同一层级下不能有重复的父目标ID，请检查目标层级设置";
    public static final String CHILDREN_NOT_ALLOWED = "当前版本仅支持单层级目标创建，请移除子目标后重试";
    public static final String TARGET_DUPLICATE = "目标已存在，相同计划下不能有重复的目标组合（目标ID+指标编码+数据类型）";

    // 成功信息
    public static final String OPERATION_SUCCESS = "业绩目标批量操作成功";
    public static final String OPERATION_FAILED = "业绩目标批量操作失败，请检查数据后重试";

    // 格式化错误信息的方法
    public static String formatPeriodTypeError(Integer periodType) {
        return String.format("无效的周期类型: %d，支持的类型：年度(0)、半年(1)、季度(2)、双月(3)、月度(4)、自定义日(9)", periodType);
    }

    public static String formatPlanIdError(Long planId) {
        return String.format("无效的计划ID: %d，该计划不存在或已被删除", planId);
    }

    public static String formatTargetDuplicateError(String metricCode,Integer metricDataType) {
        return String.format("同一指标的目标已存在，无法重复创建： 指标=%s, 数据类型=%s",
                MetricCodeEnum.getNameByCode(metricCode), MetricDataTypeEnum.getNameByCode(metricDataType));
    }
}
