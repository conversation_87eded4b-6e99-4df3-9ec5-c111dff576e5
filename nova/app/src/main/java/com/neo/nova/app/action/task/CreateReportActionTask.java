package com.neo.nova.app.action.task;

import com.neo.nova.app.action.ActionFactory;
import com.neo.nova.app.action.config.ActionConfig;
import com.neo.nova.app.action.config.RoleConfig;
import com.neo.nova.app.action.enums.RoleEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.xxljob.client.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 创建任务
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 20:47
 */
@Component("createReportActionTask")
@Slf4j
public class CreateReportActionTask {
    @Autowired
    private UserService userService;


    @XxlJob("createReportActionTask")
    public void createReportActionTask(String params) {

        //todo 获取所有用户以及权限

        String authCode = params;
        List< Long> userIds=new ArrayList<>();
        Long userId = 1L;
        userIds.add(userId);

        //todo 获取所有计划

        Map<RoleEnums, List<ActionConfig>> actionRoleConfig = RoleConfig.actionRoleConfig;
        RoleEnums byCode = RoleEnums.getByCode(authCode);
        byCode=RoleEnums.CUSTOMER_SALESMAN_DIRECTOR;
        if (byCode == null) {
            return;
        }

        for(Long id : userIds) {
            List<ActionConfig> actionConfig = actionRoleConfig.get(byCode);
            ActionTaskModel actionTaskModel = new ActionTaskModel();
            actionTaskModel.setUserId(id);
            actionTaskModel.setPriority(WorkOrderPriorityEnums.NORMAL.getCode());
            Long workOrderId = ActionFactory.initWorkOrder(actionTaskModel);
            for (ActionConfig value : actionConfig) {
                if (value == null) {
                    continue;
                }
                actionTaskModel.setActionName(value.getAction().getActionName());
                actionTaskModel.setWorkOrderDetailType(value.getType());
                actionTaskModel.setSorted(value.getSorted());
                if (workOrderId == null) {
                    // 创建失败子工单 继续下一个创建
                    log.error("初始化工单失败 actionName->{} userId->{} ", value.getAction().getActionName(), userId);
                    continue;
                }
                actionTaskModel.setWorkOrderId(workOrderId);
                value.getAction().createTask(actionTaskModel);
            }
        }
    }
}
