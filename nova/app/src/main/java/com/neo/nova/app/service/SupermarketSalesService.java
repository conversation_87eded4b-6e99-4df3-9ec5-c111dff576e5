package com.neo.nova.app.service;

import com.neo.nova.domain.entity.SupermarketSalesMCPData;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/17
 **/
public interface SupermarketSalesService {

    /**
     * 查询商超销量数据
     *
     * @param tenantId
     * @param userId
     * @param conversationId
     * @return
     */
    List<SupermarketSalesMCPData> list(Long tenantId, Long userId, Long conversationId);


    /**
     * 批量更新商超销量数据
     *
     * @param ids
     * @param updateUserId
     * @param supermarketSalesMCPData
     * @return
     */
    boolean batchUpdateByIds(List<Long> ids, Long tenantId, Long updateUserId, SupermarketSalesMCPData supermarketSalesMCPData);
}
