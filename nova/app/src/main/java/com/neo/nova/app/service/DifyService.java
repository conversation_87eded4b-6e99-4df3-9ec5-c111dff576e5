package com.neo.nova.app.service;

import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.nova.app.request.DataSourceUploadRequest;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Dify AI服务接口
 *
 * <AUTHOR>
 * @since 2025/7/27
 */
public interface DifyService {

    /**
     * 异步调用Dify AI进行数据处理
     *
     * @param request      数据源上传请求
     * @param dataSourceId 数据源ID
     * @return 异步处理结果
     */
    CompletableFuture<Response> processDataWithDifyAsync(DataSourceUploadRequest request, Long dataSourceId);

    /**
     * 同步调用Dify AI进行数据处理
     *
     * @param request      数据源上传请求
     * @param dataSourceId 数据源ID
     * @return 处理结果
     */
    SingleResponse<String> processDataWithDify(DataSourceUploadRequest request, Long dataSourceId);

    /**
     * 调用Dify聊天API
     *
     * @param query    查询内容
     * @param fileType 会话ID（可选）
     * @param userId   用户ID
     * @return Dify响应结果
     */
    SingleResponse<String> callDifyChatApi(String query, Map<String, Object> input,String fileType, String fileId, String userId);

    /**
     * 调用Dify文本生成API
     *
     * @param inputs 输入参数
     * @param userId 用户ID
     * @return Dify响应结果
     */
    Response callDifyCompletionApi(Object inputs, String userId);
}
