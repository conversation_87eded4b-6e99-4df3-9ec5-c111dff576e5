package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.CustomerMetricService;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.Metric;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustom;
import com.neo.nova.domain.entity.sqlserver.SynSlPamSaleser;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.sqlserver.SynSlPamCustomRepository;
import com.neo.nova.domain.gateway.sqlserver.SynSlPamSaleserRepository;
import com.neo.nova.infrastructure.mapper.CustomerInfoMapper;
import com.neo.nova.infrastructure.mapper.sqlserver2.SynSlPamCustom2Mapper;
import com.neo.nova.infrastructure.mapper.sqlserver2.SynSlPamSaleser2Mapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.tagcenter.domain.entity.TagLeafInfo;
import com.neo.tagcenter.infrastructure.mapper.TagLeafInfoMapper;
import com.neo.user.client.tenant.api.MobileTenantService;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public abstract class AbstractSynSlPamCustomConfigImpl<T, R> extends AbstractTableSyncConfig<SynSlPamCustom, CustomerInfo> {

    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private UserService userService;
    @Resource
    private MobileTenantService mobileTenantService;
    @Resource
    private SynSlPamSaleserRepository synSlPamSaleserRepository;
    @Resource
    private SynSlPamSaleser2Mapper synSlPamSaleser2Mapper;

    @Resource
    private SynSlPamCustomRepository synSlPamCustomRepository;

    @Resource
    private SynSlPamCustom2Mapper synSlPamCustom2Mapper;
    @Resource
    private TagLeafInfoMapper tagLeafInfoMapper;
    @Resource
    private CustomerMetricService customerMetricService;

    @Override
    protected BaseMapper<CustomerInfo> getMysqlMapper() {
        return customerInfoMapper;
    }


    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_Custom";
    }

    @Override
    public String getMysqlTableName() {
        return "CustomerInfo";
    }


    @Override
    public QueryWrapper<SynSlPamCustom> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustom> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }

    @Override
    public boolean needUpdate(SynSlPamCustom sqlServerData, CustomerInfo mysqlData) {

        //客户数量不多 直接更新就行
        return true;

//        if (sqlServerData == null || mysqlData == null) {
//            return false;
//        }
//        /**
//         * 比较价格类型
//         */
//        QueryWrapper<TagLeafInfo> tagLeafInfoQueryWrapper = new QueryWrapper<>();
//        switch (getSqlserverSource()) {
//            case SQLSERVER1:
//                tagLeafInfoQueryWrapper.eq("outId1", String.valueOf(sqlServerData.getPricetypeid()));
//                TagLeafInfo tagLeafInfo = tagLeafInfoMapper.selectOne(tagLeafInfoQueryWrapper);
//                if (tagLeafInfo != null && tagLeafInfo.getId() != mysqlData.getPriceTypeId()) {
//                    return true;
//                }
//                break;
//            case SQLSERVER2:
//                tagLeafInfoQueryWrapper.eq("outId2", String.valueOf(sqlServerData.getPricetypeid()));
//                TagLeafInfo tagLeafInfo2 = tagLeafInfoMapper.selectOne(tagLeafInfoQueryWrapper);
//                if (tagLeafInfo2 != null && tagLeafInfo2.getId() != mysqlData.getPriceTypeId()) {
//                    return true;
//                }
//                break;
//        }
//        // 比较客户编号
//        if (!Objects.equals(sqlServerData.getCustno(), mysqlData.getCode())) {
//            return true;
//        }
//
//        // 比较客户名称
//        if (!Objects.equals(sqlServerData.getCustname(), mysqlData.getName())) {
//            return true;
//        }
//
//        // 比较助记码
//        if (!Objects.equals(sqlServerData.getMnemocode(), mysqlData.getMnemoCode())) {
//            return true;
//        }
//
//        // 比较联系人
//        if (!Objects.equals(sqlServerData.getLinkman(), mysqlData.getLinkMan())) {
//            return true;
//        }
//
//        // 比较联系电话
//        if (!Objects.equals(sqlServerData.getContactnumber(), mysqlData.getContractNumber())) {
//            return true;
//        }
//
//        // 比较送货地址
//        if (!Objects.equals(sqlServerData.getTdelivyaddress(), mysqlData.getDeliveryAddress())) {
//            return true;
//        }
//
//        // 比较状态
//        if (!Objects.equals(sqlServerData.getUstate(), mysqlData.getStatus() != null && mysqlData.getStatus() == 1 ? "1" : "0")) {
//            return true;
//        }
//
//        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(CustomerInfo data) {
        return data.getOutId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlPamCustom data) {
        return data.getId();
    }

    @Override
    public CustomerInfo convertToMysqlEntity(SynSlPamCustom data) {
        return fromCustom(data);
    }

    @Override
    public void batchInsertToMysql(List<CustomerInfo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 分批插入，避免单次插入数据量过大
        int batchSize = 100;
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<CustomerInfo> batchData = dataList.subList(i, endIndex);
            for (CustomerInfo data : batchData) {
                QueryWrapper<CustomerInfo> objectQueryWrapper = new QueryWrapper<>();
                objectQueryWrapper.eq("name", data.getName());
                if (getMysqlMapper().selectOne(objectQueryWrapper) != null) {
                    continue;
                }
                //9是已删除 干掉
                if (data.getStatus() == 9) {
                    continue;
                }
                getMysqlMapper().insert(data);
            }
        }
    }

    /**
     * SynSlPamCustom转CustomerInfo
     *
     * @param custom
     * @return
     */
    public CustomerInfo fromCustom(SynSlPamCustom custom) {
        if (custom == null) {
            return null;
        }


        long now = System.currentTimeMillis() / 1000;

        CustomerInfo customerInfo = new CustomerInfo();

        //重新赋值
        custom = buildCustomerInfo(custom, customerInfo);

        customerInfo.setCode(custom.getCustno());
        customerInfo.setName(custom.getCustname());
        customerInfo.setMnemoCode(custom.getMnemocode());
        customerInfo.setLinkMan(custom.getLinkman());
        customerInfo.setContractNumber(custom.getContactnumber());
        customerInfo.setDeliveryAddress(custom.getTdelivyaddress());

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(21);

        BaseTagQueryOption option = new BaseTagQueryOption();
        option.setIncludeDisable(true);
        switch (getSqlserverSource()) {
            case SQLSERVER1:

                param.setOutId1(String.valueOf(custom.getCusttypeid()));
                break;
            case SQLSERVER2:

                param.setOutId2(String.valueOf(custom.getCusttypeid()));
                break;
        }

        MultiResponse<TagLeafInfoDto> queryResult = businessTreeTagReadReadService.queryCustomerTypeSetting(param, option);
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            customerInfo.setCustomerTypeId(tagLeafInfoDto.getId());
        }


        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1(String.valueOf(custom.getCustlineid()));
                break;
            case SQLSERVER2:
                param.setOutId2(String.valueOf(custom.getCustlineid()));
                break;
        }
        queryResult = businessTreeTagReadReadService.queryDeliveryRouteSetting(param, option);
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            customerInfo.setCustomerLineId(tagLeafInfoDto.getId());
        }

        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1(String.valueOf(custom.getCustdistrictid()));
                break;
            case SQLSERVER2:
                param.setOutId2(String.valueOf(custom.getCustdistrictid()));
                break;
        }
        queryResult = businessTreeTagReadReadService.queryAdministrativeRegionSetting(param, option);
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            customerInfo.setAdminRegionId(tagLeafInfoDto.getId());
        }

        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1(String.valueOf(custom.getCustareaid()));
                break;
            case SQLSERVER2:
                param.setOutId2(String.valueOf(custom.getCustareaid()));
                break;
        }
        queryResult = businessTreeTagReadReadService.querySalesRegionSetting(param, option);
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            customerInfo.setCustomerAreaId(tagLeafInfoDto.getId());
        }

        customerInfo.setOpenDate(custom.getOpendate().getTime() / 1000);
        customerInfo.setAuditFlag(Integer.valueOf(custom.getAuditflag()));

        customerInfo.setStatus(Integer.valueOf(custom.getUstate()));
        customerInfo.setDispPrice(Integer.valueOf(custom.getDispprice()));
        customerInfo.setCreatedBy(-1L);
        customerInfo.setCreated(now);
        customerInfo.setUpdatedBy(-1L);
        customerInfo.setUpdated(now);
        customerInfo.setTenantId(21L);
        customerInfo.setOutId(custom.getId().toString());
        customerInfo.setOutType(getSqlserverSource().getValue());

        QueryWrapper<TagLeafInfo> tagLeafInfoQueryWrapper = new QueryWrapper<>();
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                tagLeafInfoQueryWrapper.eq("outId1", String.valueOf(custom.getPricetypeid()));
                TagLeafInfo tagLeafInfo = tagLeafInfoMapper.selectOne(tagLeafInfoQueryWrapper);
                if (tagLeafInfo != null)
                    customerInfo.setPriceTypeId(tagLeafInfo.getId());
                break;
            case SQLSERVER2:
                tagLeafInfoQueryWrapper.eq("outId2", String.valueOf(custom.getPricetypeid()));
                TagLeafInfo tagLeafInfo2 = tagLeafInfoMapper.selectOne(tagLeafInfoQueryWrapper);
                if (tagLeafInfo2 != null)
                    customerInfo.setPriceTypeId(tagLeafInfo2.getId());
                break;
        }
        
 
        
       
        return customerInfo;
    }


    private SynSlPamCustom buildCustomerInfo(SynSlPamCustom custom, CustomerInfo customerInfo) {

        switch (getSqlserverSource()) {
            case SQLSERVER1:
                //如果表1状态已合作  那么直接取这个信息, 否则看下另一个表有没有已合作 的
                if (custom.getUstate() != null) {
                    //如果是正常直接取
                    if (!"1".equals(custom.getUstate())) {
                        //去2表取正常的数据
                        LambdaQueryWrapper<SynSlPamCustom> query = new LambdaQueryWrapper<>();
                        query.eq(SynSlPamCustom::getCustname, custom.getCustname());
                        List<SynSlPamCustom> synSlPamCustoms = synSlPamCustom2Mapper.selectList(query);
                        if (synSlPamCustoms != null && !synSlPamCustoms.isEmpty()) {
                            SynSlPamCustom synSlPamCustom = synSlPamCustoms.get(0);
                            if (synSlPamCustom != null && "1".equals(synSlPamCustom.getUstate())) {
                                custom = synSlPamCustom;
                            }
                        }
                    }
                }

                //查询业务员的信息
                Integer saleserid1 = custom.getSaleserid();
                SynSlPamSaleser byId = synSlPamSaleserRepository.getById(saleserid1);
                if (byId != null && StringUtils.isNotBlank(byId.getPhoneNo())) {
                    SingleResponse<UserMobileInfoDTO> userMobileInfoDTOSingleResponse = mobileTenantService.queryMobileInfoByMobile(byId.getPhoneNo(), "86", 21L);
                    if (userMobileInfoDTOSingleResponse.isSuccess()) {
                        UserMobileInfoDTO userMobileInfoDTO = userMobileInfoDTOSingleResponse.getData();
                        customerInfo.setSalesId(userMobileInfoDTO.getUserId());
                    }
                }
                break;
            case SQLSERVER2:
                LambdaQueryWrapper<SynSlPamCustom> query = new LambdaQueryWrapper<>();
                query.eq(SynSlPamCustom::getCustname, custom.getCustname());
                List<SynSlPamCustom> synSlPamCustoms = synSlPamCustomRepository.list(query);
                //查一下表1 有没有 有的话直接返回  默认用第一张表的数据
                if (synSlPamCustoms != null && !synSlPamCustoms.isEmpty()) {
                    SynSlPamCustom synSlPamCustom = synSlPamCustoms.get(0);
                    //如果都是正常状态 就返回第一个表的数据
                    if (synSlPamCustom != null &&
                            "1".equals(synSlPamCustom.getUstate())
                            && "1".equals(custom.getUstate())) {
                        custom = synSlPamCustom;
                    } else if (synSlPamCustom != null && "1".equals(synSlPamCustom.getUstate()) && !"1".equals(custom.getUstate())) {
                        custom = synSlPamCustom;
                    } else if (synSlPamCustom != null && !"1".equals(synSlPamCustom.getUstate()) && !"1".equals(custom.getUstate())) {
                        custom = synSlPamCustom;
                    }
                }
                //查询业务员的信息
                Integer saleserid2 = custom.getSaleserid();
                SynSlPamSaleser synSlPamSaleser = synSlPamSaleser2Mapper.selectById(saleserid2);
                if (synSlPamSaleser != null && StringUtils.isNotBlank(synSlPamSaleser.getPhoneNo())) {
                    SingleResponse<UserMobileInfoDTO> userMobileInfoDTOSingleResponse = mobileTenantService.queryMobileInfoByMobile(synSlPamSaleser.getPhoneNo(), "86", 21L);
                    if (userMobileInfoDTOSingleResponse.isSuccess()) {
                        UserMobileInfoDTO userMobileInfoDTO = userMobileInfoDTOSingleResponse.getData();
                        if (userMobileInfoDTO != null) {
                            customerInfo.setSalesId(userMobileInfoDTO.getUserId());
                        }
                    }
                }
        }

        Map<Long, Metric> channle = customerMetricService.listCustomerMetricCodeIds(21L, MetricCodeEnum.CHANNEL.getCode(), Collections.singletonList(customerInfo));
        Map<Long, Metric> supermaker = customerMetricService.listCustomerMetricCodeIds(21L, MetricCodeEnum.CUSTOMER_TYPE.getCode(), Collections.singletonList(customerInfo));

        Metric metric = channle.get(customerInfo.getId());
        if (metric != null) {
            customerInfo.setChannel(metric.getMetricCodeId());
        }
        Metric metric1 = supermaker.get(customerInfo.getId());
        if (metric1 != null) {
            customerInfo.setSupermarketAreaId(metric1.getMetricCodeId());
        }
        return custom;
    }

    @Override
    public void after() {
        super.after();
    }
}