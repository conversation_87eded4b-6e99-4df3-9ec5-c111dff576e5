package com.neo.nova.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.service.SalesUserService;
import com.neo.nova.app.vo.SalesUserQueryVO;
import com.neo.nova.domain.dto.SalesUserInfoDTO;
import com.neo.nova.domain.entity.SalesUser;
import com.neo.nova.domain.gateway.SalesUserRepository;
import com.neo.user.client.tenant.api.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SalesUserServiceImpl implements SalesUserService {
    @Autowired
    private SalesUserRepository salesUserRepository;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public SalesUserInfoDTO list(SalesUserQueryVO salesUserQueryVO) {

        LambdaQueryWrapper<SalesUser> wrapper = bulidWrapper(salesUserQueryVO);

        return null;
    }

    public LambdaQueryWrapper<SalesUser> bulidWrapper(SalesUserQueryVO SalesUserQueryVO) {
        LambdaQueryWrapper<SalesUser> wrapper = new LambdaQueryWrapper<SalesUser>();

        if (SalesUserQueryVO.getSalesUserIds()!=null) {
            wrapper.in(SalesUser::getUserId, SalesUserQueryVO.getSalesUserIds());
        }
        if (SalesUserQueryVO.getCustomerId()!=null) {
            wrapper.eq(SalesUser::getCustomerId, SalesUserQueryVO.getCustomerId());
        }
        if(SalesUserQueryVO.getDeptIds()!=null)
        {
            wrapper.in(SalesUser::getDeptId, SalesUserQueryVO.getDeptIds());
        }
        if (SalesUserQueryVO.getStatus()!=null) {
            wrapper.in(SalesUser::getIsDeleted, SalesUserQueryVO.getStatus());
        }
        if(SalesUserQueryVO.getMonthEnd()!=null||SalesUserQueryVO.getMonthStart()!=null)
        {
            wrapper.between(SalesUser::getMonthRate, SalesUserQueryVO.getMonthStart(), SalesUserQueryVO.getMonthEnd());
        }
        if (SalesUserQueryVO.getYearEnd()!=null||SalesUserQueryVO.getYearStart()!=null)
        {
            wrapper.between(SalesUser::getYearRate, SalesUserQueryVO.getYearStart(), SalesUserQueryVO.getYearEnd());
        }
        return wrapper;
    }

    public void UpdateSalesUserList(SalesUserQueryVO salesUserQueryVO)
    {
    List<Long> deptList = departmentService.getDeptIdsByExtra(salesUserQueryVO.getTenantId(), "sales", true).getData();

    List<Long> deptIds = departmentService.getAllUserIdByDeptIds(salesUserQueryVO.getTenantId(), deptList, true).getData();
    }

}
