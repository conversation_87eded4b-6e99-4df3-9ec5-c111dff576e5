package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.entity.OrderDetail;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesbilllist;
import com.neo.nova.infrastructure.mapper.GoodsInfoMapper;
import com.neo.nova.infrastructure.mapper.OrderDetailMapper;
import com.neo.nova.infrastructure.mapper.OrderMapper;
import jakarta.annotation.Resource;

import java.util.Objects;

public abstract class AbstractSynSlSrvSalesbilllistConfigImpl<T, R> extends AbstractTableSyncConfig<SynSlSrvSalesbilllist, OrderDetail> {

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private GoodsInfoMapper goodsInfoMapper;

    @Override
    protected BaseMapper<OrderDetail> getMysqlMapper() {
        return orderDetailMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_srv_SalesBillList";
    }

    @Override
    public String getMysqlTableName() {
        return "OrderDetail";
    }

    @Override
    public QueryWrapper<SynSlSrvSalesbilllist> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlSrvSalesbilllist> queryWrapper = new QueryWrapper<SynSlSrvSalesbilllist>();
        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }

    @Override
    public boolean needUpdate(SynSlSrvSalesbilllist sqlServerData, OrderDetail mysqlData) {
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较物料ID
        if (!Objects.equals(sqlServerData.getMateid(), mysqlData.getGoodsId() != null ? mysqlData.getGoodsId().intValue() : null)) {
            return true;
        }

        // 比较销售价格
        if (!Objects.equals(sqlServerData.getSellprice(), mysqlData.getSalePrice())) {
            return true;
        }

        // 比较数量
        if (!Objects.equals(sqlServerData.getQty(), mysqlData.getSalesQty())) {
            return true;
        }

        // 比较发货数量
        if (!Objects.equals(sqlServerData.getDelivqty(), mysqlData.getDelivyQty())) {
            return true;
        }

        // 比较单据金额
        if (!Objects.equals(sqlServerData.getBillmoney(), mysqlData.getBillMoney())) {
            return true;
        }

        // 比较发货金额
        if (!Objects.equals(sqlServerData.getDelivmoney(), mysqlData.getDelivMoney())) {
            return true;
        }

        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(OrderDetail data) {
        return data.getOutId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlSrvSalesbilllist data) {
        return data.getId();
    }

    @Override
    public OrderDetail convertToMysqlEntity(SynSlSrvSalesbilllist data) {
        return fromSalesbilllist(data);
    }

    /**
     * SynSlSrvSalesbilllist转OrderDetail
     *
     * @param salesbilllist
     * @return
     */
    public OrderDetail fromSalesbilllist(SynSlSrvSalesbilllist salesbilllist) {
        if (salesbilllist == null) {
            return null;
        }

        OrderDetail orderDetail = new OrderDetail();

        orderDetail.setOutId(String.valueOf(salesbilllist.getId()));
        orderDetail.setOutType(getSqlserverSource().getValue());

        long currentTime = System.currentTimeMillis() / 1000;

        Order order = orderMapper.selectOne(new QueryWrapper<Order>()
                .eq("outId", salesbilllist.getSalesbillid())
                .eq("outType", getSqlserverSource().getValue()));
        if (order != null) {
            orderDetail.setOrderId(order.getId());
            orderDetail.setCustomerId(order.getCustomerId());
        } else {
            orderDetail.setOrderId(-1L);
        }

        GoodsInfo goodsInfo = goodsInfoMapper.selectOne(
                new QueryWrapper<GoodsInfo>()
                        .eq("outId", salesbilllist.getMateid())
                        .eq("outType", getSqlserverSource().getValue()));
        if (goodsInfo != null) {
            orderDetail.setGoodsId(goodsInfo.getId());
        } else {
            orderDetail.setGoodsId(-1L);
        }

        orderDetail.setSalePrice(salesbilllist.getSellprice());
        orderDetail.setSalesQty(salesbilllist.getQty());
        orderDetail.setDelivyQty(salesbilllist.getDelivqty());
        orderDetail.setBillMoney(salesbilllist.getBillmoney());
        orderDetail.setDelivMoney(salesbilllist.getDelivmoney());

        orderDetail.setCreated(currentTime);
        orderDetail.setUpdated(currentTime);

        return orderDetail;
    }

    @Override
    public void after() {
        super.after();
    }
}
