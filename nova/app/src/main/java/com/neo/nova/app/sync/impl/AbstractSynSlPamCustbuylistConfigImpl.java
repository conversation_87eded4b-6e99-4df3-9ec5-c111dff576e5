package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.MultiResponse;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;
import com.neo.nova.app.service.SalesDataUpdateService;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Priceschemepurchaselist;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustbuylist;
import com.neo.nova.infrastructure.mapper.GoodsInfoMapper;
import com.neo.nova.infrastructure.mapper.PriceschemepurchaselistMapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

public abstract class AbstractSynSlPamCustbuylistConfigImpl<T, R> extends AbstractTableSyncConfig<SynSlPamCustbuylist, Priceschemepurchaselist> {

    @Resource
    private PriceschemepurchaselistMapper priceschemepurchaselistMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private GoodsInfoMapper goodsInfoMapper;

    @Override
    protected BaseMapper<Priceschemepurchaselist> getMysqlMapper() {
        return priceschemepurchaselistMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_pam_custbuylist";
    }

    @Override
    public String getMysqlTableName() {
        return "Priceschemepurchaselist";
    }

    @Override
    public void batchUpdateToMysql(List<Priceschemepurchaselist> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.forEach(res -> {
            getMysqlMapper().updateById(res);
        });
    }


    @Override
    public QueryWrapper<SynSlPamCustbuylist> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlPamCustbuylist> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }

    @Override
    public boolean needUpdate(SynSlPamCustbuylist sqlServerData, Priceschemepurchaselist mysqlData) {
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较客户类型ID
        if (!Objects.equals(sqlServerData.getCusttypeid(), mysqlData.getCustTypeID())) {
            return true;
        }

        // 比较物料ID
        if (!Objects.equals(sqlServerData.getMateid(), mysqlData.getMatelID())) {
            return true;
        }

        // 比较销售价格
        if (!Objects.equals(sqlServerData.getSellprice(), mysqlData.getSellPrice())) {
            return true;
        }

        // 比较打印价格
        if (!Objects.equals(sqlServerData.getPrintprice(), mysqlData.getPrintPrice())) {
            return true;
        }

        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(Priceschemepurchaselist data) {
        return data.getOutId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlPamCustbuylist data) {
        return data.getId();
    }

    @Override
    public Priceschemepurchaselist convertToMysqlEntity(SynSlPamCustbuylist data) {
        return fromCustbuylist(data);
    }

    /**
     * SynSlPamCustbuylist转OrderDetail
     *
     * @param custbuylist
     * @return
     */
    public Priceschemepurchaselist fromCustbuylist(SynSlPamCustbuylist custbuylist) {
        if (custbuylist == null) {
            return null;
        }

        Priceschemepurchaselist priceschemepurchaselist = new Priceschemepurchaselist();
        priceschemepurchaselist.setOutId(custbuylist.getId().toString());

        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(21);
        switch (getSqlserverSource()) {
            case SQLSERVER1:

                param.setOutId1(String.valueOf(custbuylist.getCusttypeid()));
                priceschemepurchaselist.setOutType(SqlServerSourceEnums.SQLSERVER1.getValue());
                break;
            case SQLSERVER2:
                param.setOutId2(String.valueOf(custbuylist.getCusttypeid()));
                priceschemepurchaselist.setOutType(SqlServerSourceEnums.SQLSERVER2.getValue());
                break;
        }
        MultiResponse<TagLeafInfoDto> queryResult = businessTreeTagReadReadService.queryCustomerTypeSetting(param, new BaseTagQueryOption());
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            priceschemepurchaselist.setCustTypeID(Math.toIntExact(tagLeafInfoDto.getId()));
        }

        GoodsInfo goodsInfo = goodsInfoMapper.selectOne(
                new QueryWrapper<GoodsInfo>()
                        .eq("outId", custbuylist.getMateid())
                        .eq("outType", getSqlserverSource().getValue()));
        if (goodsInfo != null) {
            priceschemepurchaselist.setMatelID(Math.toIntExact(goodsInfo.getId()));
        }

        priceschemepurchaselist.setSellPrice(custbuylist.getSellprice());
        priceschemepurchaselist.setPrintPrice(custbuylist.getPrintprice());

        return priceschemepurchaselist;
    }

    @Resource
    private SalesDataUpdateService salesDataUpdateService;

    @Override
    public void after() {
        //更新汇总图表
        salesDataUpdateService.updateSalesStatistics();
    }
}
