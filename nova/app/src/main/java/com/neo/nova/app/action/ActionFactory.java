package com.neo.nova.app.action;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.neo.nova.app.action.config.ActionConfig;
import com.neo.nova.app.action.config.RoleConfig;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.EventEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.service.WorkOrderEventService;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 21:04
 */
@Slf4j
@Component
public class ActionFactory {

    @Resource
    WorkOrderEventService workOrderEventService;

    /**
     * 执行动作
     *
     * @param actionBaseModel
     * @return
     */
    public static Boolean execute(ActionBaseModel actionBaseModel) {
        String actionName = actionBaseModel.getActionName();
        ActionConfig actionConfig = RoleConfig.actionConfig.get(actionName);
        if (actionConfig == null) {
            return false;
        }
        //todo 权限校验
        //权限校验配置
        ActionConfig actionConfig1 = RoleConfig.actionAuthConfig.get(actionName);
//        if (actionConfig1 == null) {
//            // 没有权限
//            return false;
//        }

        // 参数验证
        if (actionConfig.getAction().paramsVerification(actionBaseModel)) {
            return actionConfig.getAction().execute(actionBaseModel);
        }

        log.error("参数验证失败 {}", actionBaseModel);
        String exceptionMessage = actionBaseModel.getExceptionMessage();
        throw new BizCustomException(500, exceptionMessage);
    }


    public static Boolean createTask(ActionTaskModel actionTaskModel) {
        ActionConfig actionConfig = RoleConfig.actionAuthConfig.get(actionTaskModel.getActionName());
        if (actionConfig == null) {
            return false;
        }
        if(actionTaskModel.getSorted() == null){
            actionTaskModel.setSorted(0);
        }
        //先初始化主工单
        Long workOrderId = initWorkOrder(actionTaskModel);
        if (workOrderId == null) {
            log.error("初始化工单失败");
            return false;
        }
        actionTaskModel.setWorkOrderId(workOrderId);
        return actionConfig.getAction().createTask(actionTaskModel);
    }

    public static Long initWorkOrder(ActionTaskModel actionTaskModel) {
        //先初始化主工单
        IWorkOrderRepository bean = SpringUtil.getBean(IWorkOrderRepository.class);
        WorkOrder workOrder = new WorkOrder();
        //todo 选择创建人
        workOrder.setCreatorId(SessionContextHolder.getUserId());
        workOrder.setExecutorId(actionTaskModel.getUserId());
        workOrder.setUpdaterId(1L);
        workOrder.setTenantId(actionTaskModel.getTenantId());
        workOrder.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        workOrder.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
        workOrder.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
        workOrder.setPriority(actionTaskModel.getPriority());
        workOrder.setTenantId(actionTaskModel.getTenantId());
        workOrder.setType(actionTaskModel.getActionType());
        workOrder.setSubType(actionTaskModel.getWorkOrderDetailType());
        boolean save = bean.save(workOrder);
        if (!save) {
            log.error("初始化工单失败");
            return null;
        }

        //创建事件
        WorkOrderEventService beanEvent = SpringUtil.getBean(WorkOrderEventService.class);
        beanEvent.createWorkOrderEvent(workOrder.getId(), null, workOrder.getCreatorId(), EventEnums.CREATE.getEventType(), EventEnums.CREATE.getEventDesc());
        return workOrder.getId();
    }
}
