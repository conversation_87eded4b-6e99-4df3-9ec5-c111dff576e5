package com.neo.nova.app.action.enums;/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                   Code is far away from bug with the animal protecting
 *                   神兽保佑,代码无bug
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 20:16
 */

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ActionRelationEnums {

    COMMON("COMMON", "日常工作",0),
    CHECK_IN("checkIn", "导购工作",1),
    REPORT("report", "汇报",2),
    CHECK_AND_FIX("checkAndFix", "巡检整改",3),
    PHOTO_RECORD("photoRecord", "拍照",4),
    VISITING_RECORD("visitingRecord", "拜访",5),
    EXCEPTIONAL_APPEAL("exceptionalAppeal", "异常申诉",6),
    ORDER_MESSAGE_ALARM("orderMessageAlarm", "客户订单预警",7),
    NOTICE("notice", "通知",8);


    private String actionName;
    private String actionDesc;
    private Integer actionType;

    public static final Map<Integer, ActionRelationEnums> actionMap = new HashMap<>();
    static {
        for (ActionRelationEnums value : ActionRelationEnums.values()) {
            actionMap.put(value.actionType, value);
        }
    }

    static public String getNameByCode(Integer code) {
        for (ActionRelationEnums value : ActionRelationEnums.values()) {
            if (value.actionType.equals(code)) {
                return value.actionName;
            }
        }
        return null;
    }

    static public Integer getTypeByActionName(String actionName) {
        for (ActionRelationEnums value : ActionRelationEnums.values()) {
            if (value.actionName.equals(actionName)) {
                return value.actionType;
            }
        }
        return null;
    }
    static public String getDescByActionName(String actionName) {
        for (ActionRelationEnums value : ActionRelationEnums.values()) {
            if (value.actionName.equals(actionName)) {
                return value.actionDesc;
            }
        }
        return null;
    }

    ActionRelationEnums(String actionName, String actionDesc,Integer actionType) {
        this.actionName = actionName;
        this.actionDesc = actionDesc;
        this.actionType = actionType;
    }


}
