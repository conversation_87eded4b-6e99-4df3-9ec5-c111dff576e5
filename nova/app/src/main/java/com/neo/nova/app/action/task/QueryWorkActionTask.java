package com.neo.nova.app.action.task;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.api.SingleResponse;
import com.neo.common.time.TimeUtils;
import com.neo.nova.app.action.ActionFactory;
import com.neo.nova.app.action.actionType.CheckInTimeEnums;
import com.neo.nova.app.action.config.ActionConfig;
import com.neo.nova.app.action.config.RoleConfig;
import com.neo.nova.app.action.enums.RoleEnums;
import com.neo.nova.app.action.enums.WorkOrderDetailStatusEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.user.client.tenant.api.CheckinTenantService;
import com.neo.user.client.tenant.dto.UserCheckinDTO;
import com.neo.xxljob.client.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 创建任务
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 20:47
 */
@Component("queryWorkActionTask")
@Slf4j
public class QueryWorkActionTask {

    @Resource
    IWorkOrderDetailRepository workOrderDetailRepository;

    @Resource
    IWorkOrderRepository workOrderRepository;

    @Resource
    CheckinTenantService checkinTenantService;

    /**
     * 查询打卡任务
     *
     * @param params
     */
    @XxlJob("queryWorkActionTask")
    public void queryWorkActionTask(String params) {

        LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(WorkOrderDetail::getStatus, WorkOrderDetailStatusEnums.NOT_START.getCode());
        queryWrapper.eq(WorkOrderDetail::getAction, "checkIn");
        queryWrapper.ge(WorkOrderDetail::getWorkOrderStartTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));

        List<WorkOrderDetail> workOrderDetails =  workOrderDetailRepository.list(queryWrapper);

        //把上面list 分批成100个每批次
        List<List<WorkOrderDetail>> workOrderDetailList
                = workOrderDetails.stream().collect(Collectors.groupingBy(item -> workOrderDetails.indexOf(item) / 100)).values().stream().toList();

        for (List<WorkOrderDetail> value : workOrderDetailList) {
            if (CollectionUtils.isEmpty( value)) {
                continue;
            }

            //取出所有的执行人ID
            Set<Long> userIds = value.stream().map(WorkOrderDetail::getExecutorId).collect(Collectors.toSet());
            //查询腾讯云的上午打卡状态
            SingleResponse<Map<Long, List<UserCheckinDTO>>> resFirst = checkinTenantService.queryUserCheckinData(value.get(0).getTenantId(), new ArrayList<Long>(userIds), TimeUtils.getTodayTimestamp(), TimeUtils.getTodayTimestamp()+43200);
            //下午打卡状态
            SingleResponse<Map<Long, List<UserCheckinDTO>>> resSecond = checkinTenantService.queryUserCheckinData(value.get(0).getTenantId(), new ArrayList<Long>(userIds), TimeUtils.getTodayTimestamp()+43201, TimeUtils.getTodayTimestamp()+86400);

            //更新所有的订单状态
            for(WorkOrderDetail workOrderDetail : value){

                //相等就取上午
                if(workOrderDetail.getWorkOrderEndTime().isEqual( LocalDateTimeUtil.beginOfDay(LocalDateTime.now()).plusHours(12))) {
                    List<UserCheckinDTO> userCheckinDTOS = resFirst.getData().get(workOrderDetail.getExecutorId());

                    syncCheckInstatus(workOrderDetail, userCheckinDTOS);
                }else{
                    List<UserCheckinDTO> userCheckinDTOS = resSecond.getData().get(workOrderDetail.getExecutorId());
                    syncCheckInstatus(workOrderDetail,userCheckinDTOS);
                }

            }

        }
    }

    private void syncCheckInstatus(WorkOrderDetail workOrderDetail, List<UserCheckinDTO> userCheckinDTOS) {
        if(CollectionUtils.isEmpty(userCheckinDTOS)){
            return ;
        }
        for(UserCheckinDTO tmp : userCheckinDTOS){
            //如果相等，则说明都是上班或者都是下班
            if(tmp.getCheckinType().equals(CheckInTimeEnums.getByCheckInType(workOrderDetail.getType()))){
                if(StringUtils.isNotBlank(tmp.getExceptionType())){
                    //存在异常
                    workOrderDetail.setStatus(WorkOrderDetailStatusEnums.ABNORMAL.getCode());
                }else{
                    workOrderDetail.setStatus(WorkOrderDetailStatusEnums.COMPLETED.getCode());

                    workOrderDetail.setCompleteTime(LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(tmp.getCheckinTime()),
                            ZoneId.of("Asia/Shanghai")  // 也可以指定具体时区，如北京时间
                    ));
                }
                workOrderDetailRepository.updateById(workOrderDetail);

                LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WorkOrderDetail::getWorkOrderId, workOrderDetail.getWorkOrderId());
                queryWrapper.eq(WorkOrderDetail::getStatus, WorkOrderDetailStatusEnums.ABNORMAL.getCode());

                List<WorkOrderDetail> orderDetails = workOrderDetailRepository.list(queryWrapper);
                if(CollectionUtils.isEmpty(orderDetails)){
                    WorkOrder workOrder = new WorkOrder();
                    workOrder.setId(workOrderDetail.getWorkOrderId());
                    workOrder.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
                    queryWrapper.eq(WorkOrderDetail::getStatus, WorkOrderDetailStatusEnums.COMPLETED.getCode());
                    List<WorkOrderDetail> orderDetailCompleted = workOrderDetailRepository.list(queryWrapper);
                    if(orderDetailCompleted.size() == 4){
                        workOrder.setStatus(WorkOrderStatusEnums.COMPLETED.getCode());
                    }
                    workOrderRepository.updateById(workOrder);
                }else{
                    WorkOrder workOrder = new WorkOrder();
                    workOrder.setId(workOrderDetail.getWorkOrderId());
                    workOrder.setStatus(WorkOrderStatusEnums.ABNORMAL.getCode());
                    workOrderRepository.updateById(workOrder);
                }
            }
        }
    }
}
