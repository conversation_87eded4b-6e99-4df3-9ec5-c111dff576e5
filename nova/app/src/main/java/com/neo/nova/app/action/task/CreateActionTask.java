package com.neo.nova.app.action.task;

import com.neo.nova.app.action.ActionFactory;
import com.neo.nova.app.action.config.ActionConfig;
import com.neo.nova.app.action.config.RoleConfig;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.RoleEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.xxljob.client.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 创建任务
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 20:47
 */
@Component("createActionTask")
@Slf4j
public class CreateActionTask implements xxljobtest111 {
    @Autowired
    private UserService userService;

    @Autowired
    private CustomerInfoRepository customerInfoRepository;

    @XxlJob("createActionTask")
    public void createTask(String params) {

        //todo 获取所有用户以及权限

        String authCode = params;
        List< Long> userIds=new ArrayList<>();
        Long userId = 1L;
        userIds.add(userId);

        //todo 获取所有计划


        Map<RoleEnums, List<ActionConfig>> actionRoleConfig = RoleConfig.actionRoleConfig;
        RoleEnums byCode = RoleEnums.getByCode(authCode);
        byCode=RoleEnums.CUSTOMER_SALESMAN_DIRECTOR;
        if (byCode == null) {
            return;
        }

        for(Long id : userIds) {
            List<ActionConfig> actionConfig = actionRoleConfig.get(byCode);
            ActionTaskModel actionTaskModel = new ActionTaskModel();
            actionTaskModel.setUserId(id);
            actionTaskModel.setPriority(WorkOrderPriorityEnums.NORMAL.getCode());
            Long workOrderId = ActionFactory.initWorkOrder(actionTaskModel);
            for (ActionConfig value : actionConfig) {
                if (value == null) {
                    continue;
                }
                actionTaskModel.setActionName(value.getAction().getActionName());
                actionTaskModel.setWorkOrderDetailType(value.getType());
                actionTaskModel.setSorted(value.getSorted());
                if (workOrderId == null) {
                    // 创建失败子工单 继续下一个创建
                    log.error("初始化工单失败 actionName->{} userId->{} ", value.getAction().getActionName(), userId);
                    continue;
                }
                actionTaskModel.setWorkOrderId(workOrderId);
                value.getAction().createTask(actionTaskModel);
            }
        }
    }

    @XxlJob("createVisitTask")
    public void createVisitTask(String params) {
        //查出所有客户
        List<CustomerInfo> customerDTOS = customerInfoRepository.list();
        //对每个客户创建拜访工单
        for (CustomerInfo customerDTO : customerDTOS) {

            Integer level = customerDTO.getLevel();
            Long saleId=customerDTO.getSalesId();
            ActionTaskModel actionTaskModel = new ActionTaskModel();
            actionTaskModel.setUserId(saleId);
            actionTaskModel.setPriority(WorkOrderPriorityEnums.NORMAL.getCode());
            Long workOrderId = ActionFactory.initWorkOrder(actionTaskModel);
            actionTaskModel.setActionName(ActionRelationEnums.VISITING_RECORD.getActionName());


            actionTaskModel.setWorkOrderId(workOrderId);



        }

    }

    public void buildTask(ActionTaskModel actionTaskModel) {
        //设置每个不同action的extra
    }
}
