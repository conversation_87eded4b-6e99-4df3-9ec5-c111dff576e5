package com.neo.nova.app.vo;

import com.neo.nova.domain.dto.CustomerRemarkDTO;
import lombok.Data;
import java.util.List;

@Data
public class CustomerDetailVo {
   private Long priceTypeId;
   private String code;
    private String name;
    private String openDate;
    private String saleNumber;
    private String priceName;
    private CustomerRemarkVO customerStation;

    private String mnemoCode;
 private String linkPosition;

    /**
     * 客户联系人
     */
    private String linkMan;

    /**
     * 客户收货地址
     */
    private String deliveryAddress;

    /**
     * 客户类型id
     */
    private Long customerTypeId;

    /**
     * 客户类型名称
     */
    private String customerTypeName;

    /**
     * 客户线路id
     */
    private Long customerLineId;

    /**
     * 客户线路名称*
     */
    private String customerLineName;

    /**
     * 行政区域id
     */
    private Long adminRegionId;

    /**
     * 行政区域名称*
     */
    private String adminRegionName;

    /**
     * 客户等级
     */
    private Integer level;

    /**
     * 客户等级名称*
     */
    private String levelName;

    /**
     * 业务员Id
     */
    private Long salesId;

    /**
     * 业务员名称*
     */
    private String salesName;

    /**
     * 状态  0休眠 1使用 2停用
     */
    private Integer status;

    private  Long customerAreaId;
    private String customerAreaName;

    private String channelName;
    private String channel;
    // 联系人信息（新增）
          // 姓名
    private String contactIdentity;       // 身份
    private String contractNumber;          // 电话

    // 送货信息（勤润数据）
    private String deliveryRoute;         // 送货路线
    private String deliveryPrice;         // 送货单价格
    private String deliveryContact;       // 联系人
    private String deliveryPhone;         // 联系电话
    private String storeLocation;         // 门店点位
    private String orderDeliveryDate;     // 订单送货日期
    private String deliveryTitle;         // 送货单抬头

    // 客户标签（新增）
    private String tagContent;            // 标签内容

    // 跟进记录
    private String followUpRecordType;    // 跟进记录类型：全部/拜访记录/巡检记录/增样品记录
    private boolean showWriteFollowUp;    // 是否显示写跟进按钮：当选项内容是"跟进记录"时，展示写跟进，点击后出现弹窗，可输入文本和图片。
    private List<CustomerRemarkVO> customerRemarkVOList;
    // 财务信息（勤润数据）
    private String bankName;              // 开户行
    private String bankAccount;           // 账号
    private String accountName;           // 账户名
    private String settlementCenter;      // 结算中心
    private String paymentTerm;           // 账期
    private String collectionCycle;       // 收款周期
    private String invoiceType;           // 开票类型
    private String collector;             // 收款员
    private String orderClerk;            // 报单员
    private String firstOrderDate;        // 首张订单日期
}