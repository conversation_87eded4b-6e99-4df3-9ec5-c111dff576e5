package com.neo.nova.app.sync.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.Order;
import com.neo.nova.domain.entity.sqlserver.SynSlSrvSalesBill;
import com.neo.nova.infrastructure.mapper.CustomerInfoMapper;
import com.neo.nova.infrastructure.mapper.OrderMapper;
import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.Date;

/**
 * SQL Server 2 数据源的销售单据同步配置实现
 */
public abstract class AbstractSynSlSrvSalesBillConfigImpl<T, R> extends AbstractTableSyncConfig<SynSlSrvSalesBill, Order> {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Override
    protected BaseMapper<Order> getMysqlMapper() {
        return orderMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_sl_srv_SalesBill";
    }

    @Override
    public String getMysqlTableName() {
        return "Order";
    }


    @Override
    public QueryWrapper<SynSlSrvSalesBill> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynSlSrvSalesBill> queryWrapper = new QueryWrapper<SynSlSrvSalesBill>();
        queryWrapper.orderByAsc("id");

        return queryWrapper;
    }


    @Override
    public boolean needUpdate(SynSlSrvSalesBill sqlServerData, Order mysqlData) {
        // 如果任一数据为空，不需要更新
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较订单编号
        if (!ObjectUtil.equal(sqlServerData.getBillno(), mysqlData.getCode())) {
            return true;
        }

        // 比较客户ID
        if (!ObjectUtil.equal(sqlServerData.getCustid(), mysqlData.getCustomerId())) {
            return true;
        }

        // 比较发货时间 - 需要处理Date到Integer的转换
        if (!compareDateWithTimestamp(sqlServerData.getDelivdate(), mysqlData.getDelivyDate())) {
            return true;
        }

        // 比较订单状态
        if (!ObjectUtil.equal(sqlServerData.getBillstate(), mysqlData.getStatus())) {
            return true;
        }

        // 比较订单金额 - 需要处理BigDecimal到Long的转换
        if (!compareBillMoneyWithAmount(sqlServerData.getBillmoney(), mysqlData.getAmount())) {
            return true;
        }

        return false;
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynSlSrvSalesBill data) {
        return String.valueOf(data.getId());
    }

    @Override
    public Object getMysqlPrimaryKeyValue(Order data) {
        return data.getOutId();
    }

    @Override
    public Order convertToMysqlEntity(SynSlSrvSalesBill data) {
        Order order = new Order();

        // 设置主键映射
        order.setOutId(String.valueOf(data.getId()));

        // 设置基本信息
        order.setTenantId(21L); // 默认租户ID
        order.setCode(data.getBillno()); // 订单编号
        order.setOrderType(Integer.valueOf(data.getBilltype()));

        // 设置数据源类型为sqlserver2（关键区别）
        order.setOutType(getSqlserverSource().getValue());

        // 客户信息 - 这里需要根据custid查询客户信息，暂时设置默认值
        if (data.getCustid() != null) {
            CustomerInfo customerInfo = customerInfoMapper.selectOne(new QueryWrapper<CustomerInfo>().
                    eq("outId", data.getCustid())
                    .eq("outType", getSqlserverSource().getValue()));

            if (customerInfo != null) {
                order.setCustomerId(customerInfo.getId());
            } else {
                order.setCustomerId(-1L); // 默认客户ID
            }
        }

        // 业务员信息 - 暂时设置默认值
        order.setVerifyPerson(data.getVerifyperson());

        // 订单时间信息
        if (data.getBilldate() != null) {
            order.setOrderTime(data.getBilldate().getTime() / 1000); // 转换为时间戳（秒）
        }

        if (data.getDelivdate() != null) {
            order.setDelivyDate(data.getDelivdate().getTime() / 1000); // 转换为时间戳（秒）
        }

        // 订单金额 - 转换BigDecimal到Long（分）
        if (data.getBillmoney() != null) {
            order.setAmount(data.getBillmoney());
        }

        // 订单状态映射
        if (data.getBillstate() != null) {
            order.setStatus(mapBillStateToOrderStatus(data.getBillstate()));
        }

        // 订单来源映射
        if (data.getBillfrom() != null) {
            order.setOrderFrom(mapBillFromToOrderFrom(data.getBillfrom()));
        }

        // 设置同步相关字段
        if (data.getSynid() != null) {
            order.setSynId(data.getSynid().longValue());
        }

        if (data.getPrinttime() != null) {
            order.setPrintTime(data.getPrinttime().longValue());
        }

        // 设置审核信息
        if (data.getVerifydate() != null) {
            order.setVerifyDate(String.valueOf(data.getVerifydate().getTime() / 1000));
        }

        // 设置创建和更新时间
        long currentTime = System.currentTimeMillis() / 1000;
        order.setCreated(currentTime);
        order.setUpdated(currentTime);
        order.setCreatedBy(-1L); // 默认创建人
        order.setUpdatedBy(-1L); // 默认更新人

        return order;
    }

    /**
     * 比较Date和时间戳
     */
    private boolean compareDateWithTimestamp(Date date, Long timestamp) {
        if (date == null && timestamp == null) {
            return true;
        }
        if (date == null || timestamp == null) {
            return false;
        }
        return (date.getTime() / 1000) == timestamp;
    }

    /**
     * 比较BigDecimal和Long金额
     */
    private boolean compareBillMoneyWithAmount(BigDecimal billMoney, BigDecimal amount) {
        if (billMoney == null && amount == null) {
            return true;
        }
        if (billMoney == null || amount == null) {
            return false;
        }
        return billMoney.compareTo(amount) == 0;
    }

    /**
     * 映射单据状态到订单状态
     */
    private Integer mapBillStateToOrderStatus(String billState) {
        // 根据业务规则映射状态
        switch (billState) {
            case "0":
                return 0; // 未发货
            case "1":
                return 1; // 已发货
            case "2":
                return 2; // 结账中
            case "3":
                return 3; // 已结账
            case "9":
                return 9; // 已删除
            default:
                return 0; // 默认未发货
        }
    }

    /**
     * 映射单据来源到订单来源
     */
    private Integer mapBillFromToOrderFrom(String billFrom) {
        // 根据业务规则映射来源
        switch (billFrom) {
            case "0":
                return 0; // 电话
            case "1":
                return 1; // 手机
            case "2":
                return 2; // 网络
            default:
                return 0; // 默认电话
        }
    }

    @Override
    public void after() {
        super.after();
    }
}
