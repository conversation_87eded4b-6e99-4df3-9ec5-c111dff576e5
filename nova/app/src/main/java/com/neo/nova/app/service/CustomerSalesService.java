package com.neo.nova.app.service;

import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.app.vo.PieChartVO;
import com.neo.nova.app.vo.SalesReportQueryVO;
import com.neo.nova.domain.dto.PerformanceProgressDTO;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.excelExport.StatisticsExport;

import java.util.List;
import java.util.Map;

public interface CustomerSalesService {
    DataFormVO statistics(DataFormQueryVO dataFormQueryVO);

    DataFormVO trends(DataFormQueryVO dataFormQueryVO);

    PieChartVO pieChart(Long tenantId, String currentTime);

    PerformanceProgressDTO getProgress(Long tenantId, List<Long> userIds, TimeCondition timeCondition);

    List<StatisticsExport> StatisticsExport(DataFormQueryVO dataFormQueryVO);

    List<StatisticsExport> trendsExport(DataFormQueryVO dataFormQueryVO);

    List<Map<String, Object>> getDynamicExportData(DataFormQueryVO dataFormQueryVO);

    /**
     * 销售报表查询
     * 查询指定月份的销售数据，返回区域、负责人、门店、毛利额、订货额、实际销售额等信息
     *
     * @param queryVO 查询条件
     * @return 销售报表数据
     */
    DataFormVO salesReport(SalesReportQueryVO queryVO);
}
