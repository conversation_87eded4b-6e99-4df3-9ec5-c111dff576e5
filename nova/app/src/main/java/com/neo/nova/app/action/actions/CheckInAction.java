package com.neo.nova.app.action.actions;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.action.actionType.CheckInEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.CheckInModel;
import com.neo.nova.app.action.records.CheckInRecord;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.neo.nova.app.action.enums.WorkOrderDetailStatusEnums.COMPLETED;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 19:22
 */
@Component
@Slf4j
public class CheckInAction extends AbstractBaseAction {

    @Autowired
private IWorkOrderDetailRepository workOrderDetailRepository;
    @Autowired
    private IWorkOrderRepository workOrderRepository;

    CheckInModel params;

    /**
     * 获取动作名称
     *
     * @return
     */
    @Override
    public String getActionName() {
        return "checkIn";
    }

    /**
     * 参数验证
     *
     * @param actionBaseModel
     * @return
     */
    @Override
    public Boolean paramsVerification(ActionBaseModel actionBaseModel) {  actionBaseModel.setExceptionMessage("参数类型错误，期望ReportModel类型");

            params = (CheckInModel) actionBaseModel;
            if (params.getUserId() == null || params.getWorkOrderId() == null) {
                actionBaseModel.setExceptionMessage("参数校验失败: userId 或 workOrderId 不能为空");
                return false;
            }
            if (params.getCheckInTime() == null) {
                actionBaseModel.setExceptionMessage("参数校验失败: checkInTime 不能为空");
                return false;
            }
            return true;
    }


    /**
     * 基础执行类
     *
     * @param actionBaseModel
     */
    @Override
    public boolean execute(ActionBaseModel actionBaseModel) {
        params = (CheckInModel) actionBaseModel;
        LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderDetail::getWorkOrderId, params.getWorkOrderId());
        WorkOrderDetail workOrderDetail = workOrderDetailRepository.getOne(queryWrapper);
        workOrderDetail.setStatus(COMPLETED.getCode());
        Map<String, Object> actionDetail = new HashMap<>();
        actionDetail.put(CheckInRecord.CHECKINTIME, params.getCheckInTime());
        actionDetail.put(CheckInRecord.CHECKINLOCATION, params.getCheckInLocation());
        actionDetail.put(CheckInRecord.CHECKINDESCRIPTION, params.getCheckInDescription());
        actionDetail.put(CheckInRecord.STATUS, params.getStatus());
        workOrderDetail.setActionDetail(JSON.toJSONString(actionDetail));
        workOrderDetail.setCompleteTime(LocalDateTime.now());

        return workOrderDetailRepository.updateById(workOrderDetail);
    }

    /**
     * 创建任务
     *
     * @param actionTaskModel 任务参数
     */
    @Override
    public boolean createTask(ActionTaskModel actionTaskModel) {
        Long workOrderId = actionTaskModel.getWorkOrderId();
        if (workOrderId != null) {
            WorkOrder workOrder = workOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                log.error("签到: 无此工单");
                params.setExceptionMessage("签到: 无此工单");
                return false;
            }
            // 初始化工单
            WorkOrderDetail workOrderDetail = new WorkOrderDetail();
            workOrderDetail.setWorkOrderId(workOrderId);
            workOrderDetail.setTenantId(actionTaskModel.getTenantId());
            workOrderDetail.setAction(actionTaskModel.getActionName());
            workOrderDetail.setType(actionTaskModel.getWorkOrderDetailType());
            workOrderDetail.setExecutorId(actionTaskModel.getUserId());
            workOrderDetail.setCreatorId(SessionContextHolder.getUserId());
            workOrderDetail.setSort(actionTaskModel.getSorted());
            workOrderDetail.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
            workOrderDetail.setWorkOrderStartTime(workOrder.getWorkOrderStartTime());
            workOrderDetail.setWorkOrderEndTime(workOrder.getWorkOrderEndTime());
            workOrderDetail.setActionDetail(JSON.toJSONString(actionTaskModel.getActionDetail()));
            if(actionTaskModel.getWorkOrderDetailType() == CheckInEnums.CHECK_IN_AM.getCheckInType()
                    || actionTaskModel.getWorkOrderDetailType() == CheckInEnums.CHECK_OUT_AM.getCheckInType()){
                workOrderDetail.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
                //设置endTime是上午12点，LocalDateTime 取上午12点
                workOrderDetail.setWorkOrderEndTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()).plusHours(12));

            }else if(actionTaskModel.getWorkOrderDetailType() == CheckInEnums.CHECK_IN_PM.getCheckInType()
                    || actionTaskModel.getWorkOrderDetailType() == CheckInEnums.CHECK_OUT_PM.getCheckInType()){
                workOrderDetail.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()).plusHours(12));
                //设置endTime是上午12点，LocalDateTime 取上午12点
                workOrderDetail.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
            }

            return workOrderDetailRepository.save(workOrderDetail);
        }
        return false;
    }

}
