package com.neo.nova.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.neo.nova.app.service.CustomerMetricService;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.Metric;
import com.neo.nova.domain.entity.MetricParticipate;
import com.neo.nova.domain.enums.MetricCalcTypeEnum;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.domain.gateway.MetricParticipateRepository;
import com.neo.nova.domain.gateway.MetricRepository;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/18
 **/
@Service
public class CustomerMetricServiceImpl implements CustomerMetricService {


    @Resource
    private MetricRepository metricRepository;

    @Resource
    private MetricParticipateRepository metricParticipateRepository;

    @Resource
    private CustomerInfoRepository customerInfoRepository;


    @Override
    public Map<Long, Boolean> checkCustomerMetricCodeId(Long tenantId, String metricCode, String metricCodeId, List<Long> customerIds) {
        if (tenantId == null || CollectionUtils.isEmpty(customerIds) || metricCode == null || metricCodeId == null) {
            return new HashMap<>();
        }

        // 查询指定的指标
        LambdaQueryWrapper<Metric> metricWrapper = new LambdaQueryWrapper<>();
        metricWrapper
                .eq(Metric::getTenantId, tenantId)
                .eq(Metric::getMetricCode, metricCode)
                .eq(Metric::getMetricCodeId, metricCodeId)
                .eq(Metric::getIsDeleted, 0);
        Metric metric = metricRepository.getOne(metricWrapper);

        if (metric == null) {
            return new HashMap<>();
        }

        // 查询该指标的参与者配置
        LambdaQueryWrapper<MetricParticipate> participateWrapper = new LambdaQueryWrapper<>();
        participateWrapper
                .eq(MetricParticipate::getTenantId, tenantId)
                .eq(MetricParticipate::getMetricId, metric.getId())
                .eq(MetricParticipate::getMetricCode, metricCode)
                .eq(MetricParticipate::getMetricCodeId, metricCodeId)
                .eq(MetricParticipate::getIsDeleted, 0);
        List<MetricParticipate> participates = metricParticipateRepository.list(participateWrapper);

        if (CollectionUtils.isEmpty(participates)) {
            return new HashMap<>();
        }

        // 查询客户信息
        LambdaQueryWrapper<CustomerInfo> customerWrapper = new LambdaQueryWrapper<>();
        customerWrapper
                .in(CustomerInfo::getId, customerIds)
                .eq(CustomerInfo::getTenantId, tenantId)
                .eq(CustomerInfo::getIsDeleted, 0);
        List<CustomerInfo> customers = customerInfoRepository.list(customerWrapper);

        if (CollectionUtils.isEmpty(customers)) {
            return new HashMap<>();
        }

        // 构建客户ID到客户信息的映射
        Map<Long, CustomerInfo> customerMap = customers.stream()
                .collect(Collectors.toMap(CustomerInfo::getId, customer -> customer));

        // 构建参与者配置的映射：participateMetricCode -> calcType  -> List<participateId>
        Map<String, Map<Integer, List<Long>>> participateMap = new HashMap<>();
        for (MetricParticipate participate : participates) {
            participateMap
                    .computeIfAbsent(participate.getParticipateMetricCode(), k -> Maps.newHashMap())
                    .computeIfAbsent(participate.getCalcType(), k -> Lists.newArrayList())
                    .add(participate.getId());
        }

        // 检查每个客户是否匹配指标条件
        Map<Long, Boolean> result = new HashMap<>();
        for (Long customerId : customerIds) {
            CustomerInfo customer = customerMap.get(customerId);
            if (customer == null) {
                result.put(customerId, false);
                continue;
            }

            boolean matches = checkCustomerMatchesMetric(customer, participateMap);
            result.put(customerId, matches);
        }

        return result;
    }

    @Override
    public Map<Long, Metric> getCustomerMetricCodeIds(Long tenantId, String metricCode, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds) || metricCode == null || tenantId == null) {
            return new HashMap<>();
        }
        // 查询客户信息
        LambdaQueryWrapper<CustomerInfo> customerWrapper = new LambdaQueryWrapper<>();
        customerWrapper
                .in(CustomerInfo::getId, customerIds)
                .eq(CustomerInfo::getTenantId, tenantId)
                .eq(CustomerInfo::getIsDeleted, 0);
        List<CustomerInfo> customers = customerInfoRepository.list(customerWrapper);

        if (CollectionUtils.isEmpty(customers)) {
            return new HashMap<>();
        }

        return listCustomerMetricCodeIds(tenantId, metricCode, customers);

    }

    @Override
    public Map<Long, Metric> listCustomerMetricCodeIds(Long tenantId, String metricCode, List<CustomerInfo> customers) {
        if (CollectionUtils.isEmpty(customers) || metricCode == null || tenantId == null) {
            return new HashMap<>();
        }

        // 查询指定metricCode的所有指标
        LambdaQueryWrapper<Metric> metricWrapper = new LambdaQueryWrapper<>();
        metricWrapper
                .eq(Metric::getMetricCode, metricCode)
                .eq(Metric::getTenantId, tenantId)
                .eq(Metric::getIsDeleted, 0);
        List<Metric> metrics = metricRepository.list(metricWrapper);

        if (CollectionUtils.isEmpty(metrics)) {
            return new HashMap<>();
        }

        // 提取所有指标ID
        List<Long> metricIds = metrics.stream()
                .map(Metric::getId)
                .collect(Collectors.toList());

        // 批量查询所有指标的参与者配置
        LambdaQueryWrapper<MetricParticipate> participateWrapper = new LambdaQueryWrapper<>();
        participateWrapper
                .in(MetricParticipate::getMetricId, metricIds)
                .eq(MetricParticipate::getTenantId, tenantId)
                .eq(MetricParticipate::getIsDeleted, 0);
        List<MetricParticipate> allParticipates = metricParticipateRepository.list(participateWrapper);

        if (CollectionUtils.isEmpty(allParticipates)) {
            return new HashMap<>();
        }

        // 按指标ID分组参与者配置
        Map<Long, List<MetricParticipate>> participatesByMetricId = allParticipates.stream()
                .collect(Collectors.groupingBy(MetricParticipate::getMetricId));

        Map<Long, Metric> result = new HashMap<>();

        // 为每个指标匹配客户
        for (Metric metric : metrics) {
            List<MetricParticipate> participates = participatesByMetricId.get(metric.getId());

            if (CollectionUtils.isEmpty(participates)) {
                continue;
            }

            // 构建参与者配置的映射：participateMetricCode -> Set<participateId>
            Map<String, Map<Integer, List<Long>>> participateMap = new HashMap<>();
            for (MetricParticipate participate : participates) {
                participateMap
                        .computeIfAbsent(participate.getParticipateMetricCode(), k -> new HashMap<>())
                        .computeIfAbsent(participate.getCalcType(), k -> new ArrayList<>())
                        .add(participate.getParticipateId());
            }

            // 检查每个客户是否匹配当前指标
            for (CustomerInfo customerInfo : customers) {
                Long customerId = customerInfo.getId();
                if (result.containsKey(customerId)) {
                    continue; // 已经找到匹配的指标，跳过
                }


                boolean matches = checkCustomerMatchesMetric(customerInfo, participateMap);
                if (matches) {
                    result.put(customerId, metric);
                }
            }
        }

        return result;
    }

    @Override
    public Metric queryCustomerMetricCodeId(Long tenantId, String metricCode, CustomerInfo customerInfo) {
        if (customerInfo == null || metricCode == null || tenantId == null) {
            return null;
        }

        // 查询指定metricCode的所有指标
        LambdaQueryWrapper<Metric> metricWrapper = new LambdaQueryWrapper<>();
        metricWrapper
                .eq(Metric::getMetricCode, metricCode)
                .eq(Metric::getTenantId, tenantId)
                .eq(Metric::getIsDeleted, 0);
        List<Metric> metrics = metricRepository.list(metricWrapper);

        if (CollectionUtils.isEmpty(metrics)) {
            return null;
        }

        // 提取所有指标ID
        List<Long> metricIds = metrics.stream()
                .map(Metric::getId)
                .collect(Collectors.toList());

        // 批量查询所有指标的参与者配置
        LambdaQueryWrapper<MetricParticipate> participateWrapper = new LambdaQueryWrapper<>();
        participateWrapper
                .in(MetricParticipate::getMetricId, metricIds)
                .eq(MetricParticipate::getTenantId, tenantId)
                .eq(MetricParticipate::getIsDeleted, 0);
        List<MetricParticipate> allParticipates = metricParticipateRepository.list(participateWrapper);

        if (CollectionUtils.isEmpty(allParticipates)) {
            return null;
        }
        // 按指标ID分组参与者配置
        Map<Long, List<MetricParticipate>> participatesByMetricId = allParticipates.stream()
                .collect(Collectors.groupingBy(MetricParticipate::getMetricId));


        // 为每个指标匹配客户
        for (Metric metric : metrics) {
            List<MetricParticipate> participates = participatesByMetricId.get(metric.getId());

            if (CollectionUtils.isEmpty(participates)) {
                continue;
            }

            // 构建参与者配置的映射：participateMetricCode -> Set<participateId>
            Map<String, Map<Integer, List<Long>>> participateMap = new HashMap<>();
            for (MetricParticipate participate : participates) {
                participateMap
                        .computeIfAbsent(participate.getParticipateMetricCode(), k -> new HashMap<>())
                        .computeIfAbsent(participate.getCalcType(), k -> new ArrayList<>())
                        .add(participate.getParticipateId());
            }

            // 是否匹配当前指标
            boolean matches = checkCustomerMatchesMetric(customerInfo, participateMap);
            if (matches) {
                return metric;
            }
        }
        return null;
    }

    /**
     * 检查客户是否匹配指标的参与者条件
     *
     * @param customer       客户信息
     * @param participateMap 参与者配置映射：participateMetricCode -> Set<participateId>
     * @return 是否匹配
     */
    private boolean checkCustomerMatchesMetric(CustomerInfo customer, Map<String, Map<Integer, List<Long>>> participateMap) {
        if (CollectionUtils.isEmpty(participateMap)) {
            return false;
        }

        // 检查所有参与者条件，需要全部匹配（AND逻辑）
        for (Map.Entry<String, Map<Integer, List<Long>>> entry : participateMap.entrySet()) {
            String participateMetricCode = entry.getKey();
            Map<Integer, List<Long>> participateCalcMap = entry.getValue();

            if (CollectionUtils.isEmpty(participateCalcMap)) {
                continue;
            }

            boolean matched = false;
            List<Long> participateIds = participateCalcMap.get(MetricCalcTypeEnum.METRIC_PARTICIPATE_IN.getCode());
            if (!CollectionUtils.isEmpty(participateIds)) {
                matched = _checkCustomerMatchesMetric_in(customer, participateMetricCode, participateIds);
            }
            participateIds = participateCalcMap.get(MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN.getCode());
            if (!CollectionUtils.isEmpty(participateIds)) {
                matched = _checkCustomerMatchesMetric_not_in(customer, participateMetricCode, participateIds);
            }

            //有一个匹配不上就返回false
            if (!matched) {
                return false;
            }

        }

        return true; // 所有条件都匹配
    }

    private boolean _checkCustomerMatchesMetric_in(CustomerInfo customer, String participateMetricCode, List<Long> participateIds) {
        boolean matched = false;

        // 根据participateMetricCode判断需要检查客户的哪个属性
        switch (participateMetricCode) {
            case "D_CUSTOMER_TYPE":
                // 检查客户类型ID
                if (customer.getCustomerTypeId() != null &&
                        participateIds.contains(customer.getCustomerTypeId())) {
                    matched = true;
                }
                break;

            case "D_CUSTOMER_ADMIN_REGION":
                // 检查行政区域ID
                if (customer.getAdminRegionId() != null &&
                        participateIds.contains(customer.getAdminRegionId())) {
                    matched = true;
                }
                break;

            case "D_CUSTOMER_SALES_REGION":
                // 检查销售区域ID
                if (customer.getCustomerAreaId() != null &&
                        participateIds.contains(customer.getCustomerAreaId())) {
                    matched = true;
                }
                break;

            case "D_CUSTOMER_LEVEL":
                // 检查客户等级
                if (customer.getLevel() != null &&
                        participateIds.contains(customer.getLevel().longValue())) {
                    matched = true;
                }
                break;

            case "D_CUSTOMER_OWNER":
                // 检查客户负责人
                if (customer.getSalesId() != null &&
                        participateIds.contains(customer.getSalesId())) {
                    matched = true;
                }
                break;

            case "D_CUSTOMER":
                // 检查客户ID本身
                if (participateIds.contains(customer.getId())) {
                    matched = true;
                }
                break;

            default:
                // 未知的participateMetricCode
                matched = false;
                break;
        }

        return matched;
    }


    private boolean _checkCustomerMatchesMetric_not_in(CustomerInfo customer, String participateMetricCode, List<Long> participateIds) {
        boolean matched = true;

        // 根据participateMetricCode判断需要检查客户的哪个属性
        switch (participateMetricCode) {
            case "D_CUSTOMER_TYPE":
                // 检查客户类型ID
                if (customer.getCustomerTypeId() != null &&
                        participateIds.contains(customer.getCustomerTypeId())) {
                    matched = false;
                }
                break;

            case "D_CUSTOMER_ADMIN_REGION":
                // 检查行政区域ID
                if (customer.getAdminRegionId() != null &&
                        participateIds.contains(customer.getAdminRegionId())) {
                    matched = false;
                }
                break;

            case "D_CUSTOMER_SALES_REGION":
                // 检查销售区域ID
                if (customer.getCustomerAreaId() != null &&
                        participateIds.contains(customer.getCustomerAreaId())) {
                    matched = false;
                }
                break;

            case "D_CUSTOMER_LEVEL":
                // 检查客户等级
                if (customer.getLevel() != null &&
                        participateIds.contains(customer.getLevel().longValue())) {
                    matched = false;
                }
                break;

            case "D_CUSTOMER_OWNER":
                // 检查客户负责人
                if (customer.getSalesId() != null &&
                        participateIds.contains(customer.getSalesId())) {
                    matched = false;
                }
                break;

            case "D_CUSTOMER":
                // 检查客户ID本身
                if (participateIds.contains(customer.getId())) {
                    matched = false;
                }
                break;

            default:
                // 未知的participateMetricCode
                matched = false;
                break;
        }

        return matched;
    }

    @Override
    public Map<Long, CustomerInfo> queryMapBySalesIds(Long tenantId, List<Long> salesIds) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(salesIds)) {
            LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerInfo::getTenantId, tenantId)
                    .eq(CustomerInfo::getIsDeleted, 0)
                    .in(CustomerInfo::getSalesId, salesIds);

            return customerInfoRepository.list(wrapper).stream().collect(Collectors.toMap(CustomerInfo::getSalesId, customerInfo -> customerInfo, (v1, v2) -> v2));
        }

        return Map.of();
    }

}
