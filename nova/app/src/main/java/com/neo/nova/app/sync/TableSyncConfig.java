package com.neo.nova.app.sync;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.neo.nova.app.action.enums.SqlServerSourceEnums;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 表同步配置接口
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
public interface TableSyncConfig<T, R> {

    /**
     * 获取SQL Server表名
     */
    String getSqlServerTableName();

    /**
     * 获取SQL Server数据源
     */
    SqlServerSourceEnums getSqlserverSource();

    /**
     * 获取MySQL表名
     */
    String getMysqlTableName();

    /**
     * 获取更新数据查询条件
     */
    QueryWrapper<T> getSqlServerUpdateQueryWrapper();

    /**
     * 从SQL Server查询一个月内的更新数据
     *
     * @param startTime 开始时间（一个月前）
     * @param endTime   结束时间（当前时间）
     * @return 更新的数据列表
     */
    List<T> queryUpdatedDataFromSqlServer(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 分页查询SQL Server更新数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param offset    偏移量
     * @param limit     限制数量
     * @return 更新的数据列表
     */
    List<T> queryUpdatedDataFromSqlServerWithPagination(LocalDateTime startTime, LocalDateTime endTime, int offset, int limit);

    /**
     * 获取SQL Server更新数据总数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 总记录数
     */
    long countUpdatedDataFromSqlServer(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 从MySQL查询现有数据
     *
     * @param primaryKeys 主键列表
     * @return 现有数据Map，key为主键值
     */
    Map<Object, R> queryExistingDataFromMysql(List<Object> primaryKeys);

    /**
     * 批量插入到MySQL
     *
     * @param dataList 要插入的数据列表
     */
    void batchInsertToMysql(List<R> dataList);

    /**
     * 批量更新到MySQL
     *
     * @param dataList 要更新的数据列表
     */
    void batchUpdateToMysql(List<R> dataList);

    /**
     * 判断数据是否需要更新（扩展点）
     *
     * @param sqlServerData SQL Server中的数据
     * @param mysqlData     MySQL中的现有数据
     * @return true表示需要更新
     */
    boolean needUpdate(T sqlServerData, R mysqlData);

    /**
     * 获取数据的主键值
     *
     * @param data 数据对象
     * @return 主键值
     */
    Object getMysqlPrimaryKeyValue(R data);

    /**
     * 获取数据的主键值
     *
     * @param data 数据对象
     * @return 主键值
     */
    Object getSqlServerPrimaryKeyValue(T data);


    R convertToMysqlEntity(T data);

    void after();

}
