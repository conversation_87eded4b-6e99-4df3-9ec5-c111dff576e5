package com.neo.nova.app.sync;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 表同步配置抽象基类
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
public abstract class AbstractTableSyncConfig<T, R> implements TableSyncConfig<T, R> {

    /**
     * 获取SQL Server的Mapper
     */
    protected abstract BaseMapper<T> getSqlServerMapper();

    /**
     * 获取MySQL的Mapper
     */
    protected abstract BaseMapper<R> getMysqlMapper();

    @Override
    public List<T> queryUpdatedDataFromSqlServer(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("从SQL Server查询表{}的更新数据，时间范围：{} - {}", getSqlServerTableName(), startTime, endTime);

        // 直接使用Mapper查询，数据源由@DS注解指定
        return getSqlServerMapper().selectList(getSqlServerUpdateQueryWrapper());
    }

    @Override
    public List<T> queryUpdatedDataFromSqlServerWithPagination(LocalDateTime startTime, LocalDateTime endTime, int offset, int limit) {
        log.debug("分页查询SQL Server表{}的更新数据，时间范围：{} - {}，偏移量：{}，限制：{}",
                getSqlServerTableName(), startTime, endTime, offset, limit);

        // 使用MyBatis-Plus标准分页，动态分页拦截器会自动处理SQL方言
        int pageNum = (offset / limit) + 1;
        Page<T> page = new Page<>(pageNum, limit);
        Page<T> resultPage = getSqlServerMapper().selectPage(page, getSqlServerUpdateQueryWrapper());
        return resultPage.getRecords();
    }

    @Override
    public long countUpdatedDataFromSqlServer(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("统计SQL Server表{}的更新数据总数，时间范围：{} - {}", getSqlServerTableName(), startTime, endTime);

        try {
            // 使用专门的COUNT查询QueryWrapper，不包含ORDER BY子句
            return getSqlServerMapper().selectCount(getSqlServerCountQueryWrapper());
        } catch (Exception e) {
            log.error("SQL Server COUNT查询失败: {}", e.getMessage(), e);
            // 如果COUNT查询失败，尝试查询所有数据并计算数量（性能较差但可用）
            try {
                List<T> allData = getSqlServerMapper().selectList(getSqlServerUpdateQueryWrapper());
                return allData.size();
            } catch (Exception fallbackException) {
                log.error("SQL Server回退COUNT查询也失败: {}", fallbackException.getMessage(), fallbackException);
                throw new RuntimeException("无法统计SQL Server表" + getSqlServerTableName() + "的数据总数", fallbackException);
            }
        }
    }

    @Override
    public Map<Object, R> queryExistingDataFromMysql(List<Object> primaryKeys) {
        if (CollectionUtils.isEmpty(primaryKeys)) {
            return new HashMap<>();
        }

        log.debug("从MySQL查询表{}的现有数据，主键数量：{}", getMysqlTableName(), primaryKeys.size());

        List<R> existingList = new ArrayList<>();
        for (List<Object> objects : Lists.partition(primaryKeys, 50)) {
            QueryWrapper<R> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("outType", getSqlserverSource().getValue());
            queryWrapper.in("outId", objects);
            existingList.addAll(getMysqlMapper().selectList(queryWrapper));
        }

        Map<Object, R> resultMap = new HashMap<>();
        for (R data : existingList) {
            Object primaryKey = getMysqlPrimaryKeyValue(data);
            resultMap.put(primaryKey, data);
        }

        return resultMap;
    }

    @Override
    public void batchInsertToMysql(List<R> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.debug("批量插入MySQL表{}，数据量：{}", getMysqlTableName(), dataList.size());

        // 分批插入，避免单次插入数据量过大
        int batchSize = 100;
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<R> batchData = dataList.subList(i, endIndex);
            
            for (R data : batchData) {
                getMysqlMapper().insert(data);
            }
        }
    }
    
    
    

    @Override
    public void batchUpdateToMysql(List<R> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        log.debug("批量更新MySQL表{}，数据量：{}", getMysqlTableName(), dataList.size());

        for (R data : dataList) {
            QueryWrapper<R> objectQueryWrapper = new QueryWrapper<>();
            objectQueryWrapper.eq("outType", getSqlserverSource().getValue());
            objectQueryWrapper.eq("outId", getMysqlPrimaryKeyValue(data));
            getMysqlMapper().update(data, objectQueryWrapper);
        }
    }

    /**
     * 获取SQL Server COUNT查询的QueryWrapper
     * 默认返回空的QueryWrapper，子类可以重写添加WHERE条件
     * 注意：不要在这里添加ORDER BY子句，因为SQL Server的COUNT查询不支持ORDER BY
     */
    protected QueryWrapper<T> getSqlServerCountQueryWrapper() {
        return new QueryWrapper<>();
    }

    /**
     * 之后需要处理的 数据
     */
    public void after() {
        log.debug("开始表更新完后续事项");
    }


}
