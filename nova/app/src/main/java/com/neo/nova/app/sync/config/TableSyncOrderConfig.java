package com.neo.nova.app.sync.config;

import com.neo.nova.app.sync.TableSyncConfig;
import com.neo.nova.app.sync.impl.sqlserver.*;
import com.neo.nova.app.sync.impl.sqlserver2.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 表同步顺序配置
 *
 * <AUTHOR>
 * @since 2025/7/22
 */
@Slf4j
@Component
public class TableSyncOrderConfig {

    /**
     * 是否在遇到错误时继续执行后续同步
     */
    private boolean continueOnError = true;

    /**
     * 禁用的配置类名集合
     */
    private Set<String> disabledConfigs = new HashSet<>();

    /**
     * 自定义优先级配置 (类名 -> 优先级，数字越小优先级越高)
     */
    private Map<String, Integer> customPriorities = new HashMap<>();

    /**
     * 预定义的同步顺序配置
     * 按业务逻辑排序：基础数据 -> 业务数据
     */
    private static final Map<Class<?>, SyncOrder> PREDEFINED_ORDER = new HashMap<>();

    static {
        // 第一优先级：基础标签数据
        PREDEFINED_ORDER.put(SynIvPamMateTypeConfigImpl.class, new SyncOrder(1, "物料类型标签"));
        PREDEFINED_ORDER.put(SynIvPamMateTypeConfig2Impl.class, new SyncOrder(1, "物料类型标签2"));

        PREDEFINED_ORDER.put(SynSlPamCusttypeConfigImpl.class, new SyncOrder(2, "客户类型标签"));
        PREDEFINED_ORDER.put(SynSlPamCusttypeConfig2Impl.class, new SyncOrder(2, "客户类型标签2"));

        PREDEFINED_ORDER.put(SynSlPamCustareaConfigImpl.class, new SyncOrder(3, "客户区域标签"));
        PREDEFINED_ORDER.put(SynSlPamCustareaConfig2Impl.class, new SyncOrder(3, "客户区域标签2"));

        PREDEFINED_ORDER.put(SynSlPamCustlineConfigImpl.class, new SyncOrder(4, "客户线路标签"));
        PREDEFINED_ORDER.put(SynSlPamCustlineConfig2Impl.class, new SyncOrder(4, "客户线路标签2"));

        PREDEFINED_ORDER.put(SynSlPamCustdistrictConfigImpl.class, new SyncOrder(5, "客户区域标签"));
        PREDEFINED_ORDER.put(SynSlPamCustdistrictConfig2Impl.class, new SyncOrder(5, "客户区域标签2"));

        PREDEFINED_ORDER.put(SynSlPamPricetypeConfigImpl.class, new SyncOrder(6, "价格类型标签"));
        PREDEFINED_ORDER.put(SynSlPamPricetypeConfig2Impl.class, new SyncOrder(6, "价格类型标签2"));

        PREDEFINED_ORDER.put(SynSlPamSaleserConfigImpl.class, new SyncOrder(7, "销售员标签"));
        PREDEFINED_ORDER.put(SynSlPamSaleserConfig2Impl.class, new SyncOrder(7, "销售员标签2"));

        // 第二优先级：基础业务数据
        PREDEFINED_ORDER.put(SynSlPamCustbuylistConfigImpl.class, new SyncOrder(30, "客户采购清单"));
        PREDEFINED_ORDER.put(SynSlPamCustbuylistConfig2Impl.class, new SyncOrder(30, "客户采购清单2"));

        PREDEFINED_ORDER.put(SynIvPamMaterialConfigImpl.class, new SyncOrder(10, "物料信息"));
        PREDEFINED_ORDER.put(SynIvPamMaterialConfig2Impl.class, new SyncOrder(10, "物料信息2"));

        PREDEFINED_ORDER.put(SynSlPamCustomConfigImpl.class, new SyncOrder(11, "客户信息"));
        PREDEFINED_ORDER.put(SynSlPamCustomConfig2Impl.class, new SyncOrder(11, "客户信息2"));

        // 第三优先级：业务单据数据
        PREDEFINED_ORDER.put(SynSlSrvSalesBillConfigImpl.class, new SyncOrder(20, "销售单据"));
        PREDEFINED_ORDER.put(SynSlSrvSalesBillConfig2Impl.class, new SyncOrder(20, "销售单据2"));

        PREDEFINED_ORDER.put(SynSlSrvSalesbilllistConfigImpl.class, new SyncOrder(21, "销售单据明细"));
        PREDEFINED_ORDER.put(SynSlSrvSalesbilllistConfig2Impl.class, new SyncOrder(21, "销售单据明细2"));
    }

    /**
     * 获取按顺序排列的配置列表
     */
    public List<TableSyncConfig<?, ?>> getOrderedConfigs(List<TableSyncConfig<?, ?>> allConfigs) {
        log.info("开始排序表同步配置，总数: {}", allConfigs.size());

        List<TableSyncConfig<?, ?>> orderedConfigs = allConfigs.stream().filter(config ->
                        config.getClass() == SynSlPamCustomConfigImpl.class ||
                                config.getClass() == SynSlPamCustomConfig2Impl.class)
                .sorted(this::compareConfigs)
                .toList();

        // 打印排序结果
        log.info("表同步配置排序完成:");
        for (int i = 0; i < orderedConfigs.size(); i++) {
            TableSyncConfig<?, ?> config = orderedConfigs.get(i);
            SyncOrder order = PREDEFINED_ORDER.get(config.getClass());
            String description = order != null ? order.getDescription() : "未定义";
            int priority = getConfigPriority(config);
            boolean enabled = isConfigEnabled(config);

            log.info("  {}. {} (优先级: {}, 描述: {}, 状态: {})",
                    i + 1,
                    config.getClass().getSimpleName(),
                    priority,
                    description,
                    enabled ? "启用" : "禁用");
        }

        return orderedConfigs;
    }

    /**
     * 比较两个配置的优先级
     */
    private int compareConfigs(TableSyncConfig<?, ?> config1, TableSyncConfig<?, ?> config2) {
        int priority1 = getConfigPriority(config1);
        int priority2 = getConfigPriority(config2);

        if (priority1 != priority2) {
            return Integer.compare(priority1, priority2);
        }

        // 优先级相同时，按类名排序保证稳定性
        return config1.getClass().getSimpleName().compareTo(config2.getClass().getSimpleName());
    }

    /**
     * 获取配置的优先级
     */
    private int getConfigPriority(TableSyncConfig<?, ?> config) {
        String className = config.getClass().getSimpleName();

        // 首先检查自定义优先级
        if (customPriorities.containsKey(className)) {
            return customPriorities.get(className);
        }

        // 然后检查预定义优先级
        SyncOrder order = PREDEFINED_ORDER.get(config.getClass());
        if (order != null) {
            return order.getPriority();
        }

        // 默认优先级
        return 999;
    }

    /**
     * 检查配置是否启用
     */
    public boolean isConfigEnabled(TableSyncConfig<?, ?> config) {
        String className = config.getClass().getSimpleName();
        return !disabledConfigs.contains(className);
    }

    /**
     * 启用指定配置
     */
    public void enableConfig(String className) {
        disabledConfigs.remove(className);
        log.info("启用配置: {}", className);
    }

    /**
     * 禁用指定配置
     */
    public void disableConfig(String className) {
        disabledConfigs.add(className);
        log.info("禁用配置: {}", className);
    }

    /**
     * 设置自定义优先级
     */
    public void setCustomPriority(String className, int priority) {
        customPriorities.put(className, priority);
        log.info("设置自定义优先级: {} -> {}", className, priority);
    }

    // Getters and Setters
    public boolean isContinueOnError() {
        return continueOnError;
    }

    public void setContinueOnError(boolean continueOnError) {
        this.continueOnError = continueOnError;
    }

    public Set<String> getDisabledConfigs() {
        return disabledConfigs;
    }

    public void setDisabledConfigs(Set<String> disabledConfigs) {
        this.disabledConfigs = disabledConfigs;
    }

    public Map<String, Integer> getCustomPriorities() {
        return customPriorities;
    }

    public void setCustomPriorities(Map<String, Integer> customPriorities) {
        this.customPriorities = customPriorities;
    }

    /**
     * 同步顺序内部类
     */
    private static class SyncOrder {
        private final int priority;
        private final String description;

        public SyncOrder(int priority, String description) {
            this.priority = priority;
            this.description = description;
        }

        public int getPriority() {
            return priority;
        }

        public String getDescription() {
            return description;
        }
    }
}
