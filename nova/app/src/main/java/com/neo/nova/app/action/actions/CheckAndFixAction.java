package com.neo.nova.app.action.actions;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.CheckAndFixModel;
import com.neo.nova.app.action.records.CheckAndFixRecord;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.neo.nova.app.action.enums.WorkOrderDetailStatusEnums.COMPLETED;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明: 巡检整改
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/11 11:31
 */
@Slf4j
@Component
public class CheckAndFixAction extends AbstractBaseAction {

    @Autowired
    private IWorkOrderRepository workOrderRepository;

    @Autowired
    private CustomerRemarkService customerRemarkService;
    @Autowired
    private IWorkOrderDetailRepository workOrderDetailRepository;


    /**
     * 获取动作名称
     *
     * @return
     */
    @Override
    public String getActionName() {
        return "checkAndFix";
    }

    /**
     * 参数验证
     *
     * @param actionBaseModel
     * @return
     */
    @Override
    public Boolean paramsVerification(ActionBaseModel actionBaseModel) {
        ActionBaseModel params = actionBaseModel;
        // 校验必填字段
        if (params.getUserId() == null || params.getWorkOrderId() == null ||
                params.getWorkOrderDetailId() == null) {
            actionBaseModel.setExceptionMessage("参数校验失败：缺少必要参数");
            return false;
        }
        // 校验整改描述长度限制
        if (params.getExtraInfo() != null && params.getExtraInfo().get("fixDescription") != null
                && String.valueOf(params.getExtraInfo().get("fixDescription")).length() > 500) {
            actionBaseModel.setExceptionMessage("参数校验失败：整改描述超过最大长度限制");
            return false;
        }

        return true;
    }

    /**
     * 基础执行类
     *
     * @param actionBaseModel
     */
    @Override
    public boolean execute(ActionBaseModel actionBaseModel) {

        ActionBaseModel params = actionBaseModel;
        LambdaQueryWrapper<WorkOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderDetail::getId, params.getWorkOrderDetailId());
        WorkOrderDetail workOrderDetail = workOrderDetailRepository.getOne(queryWrapper);
        workOrderDetail.setStatus(COMPLETED.getCode());

        Map<String, Object> actionDetail = new HashMap<>();
        //不要丢了原来的东西
        if(StringUtils.hasText(workOrderDetail.getActionDetail())){
            actionDetail.putAll(JSONObject.parseObject(workOrderDetail.getActionDetail(), Map.class));
        }

        //添加新的
        if(MapUtils.isNotEmpty(actionBaseModel.getExtraInfo())){
            actionDetail.putAll(actionBaseModel.getExtraInfo());
        }

        workOrderDetail.setActionDetail(JSON.toJSONString(actionDetail));
        workOrderDetail.setCompleteTime(LocalDateTime.now());

        //接入企业的实践
        customerRemarkService.addCustomerRemarkByAction(workOrderDetail);

        return workOrderDetailRepository.updateById(workOrderDetail);
    }

    /**
     * @param actionTaskModel
     * @return
     */
    @Override
    public boolean createTask(ActionTaskModel actionTaskModel) {
        Long workOrderId = actionTaskModel.getWorkOrderId();
        if (workOrderId != null) {
            WorkOrder workOrder = workOrderRepository.getById(workOrderId);
            if (workOrder == null) {
                log.error("巡检整改: 无此工单");
                return false;
            }
            // 初始化工单
            WorkOrderDetail workOrderDetail = new WorkOrderDetail();
            workOrderDetail.setWorkOrderId(workOrderId);
            workOrderDetail.setType(actionTaskModel.getWorkOrderDetailType());
            workOrderDetail.setAction(actionTaskModel.getActionName());
            workOrderDetail.setExecutorId(actionTaskModel.getUserId());
            workOrderDetail.setCreatorId(SessionContextHolder.getUserId());
            workOrderDetail.setSort(actionTaskModel.getSorted());
            workOrderDetail.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
            workOrderDetail.setWorkOrderStartTime(workOrder.getWorkOrderStartTime());
            workOrderDetail.setWorkOrderEndTime(workOrder.getWorkOrderEndTime());
            return workOrderDetailRepository.save(workOrderDetail);
        }
        return false;
    }
}
