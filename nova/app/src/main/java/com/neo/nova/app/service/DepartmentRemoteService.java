package com.neo.nova.app.service;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.client.tenant.api.DepartmentService;
import com.neo.user.client.tenant.api.UserTenantService;
import com.neo.user.client.tenant.dto.TenantUserInfoDTO;
import com.neo.user.client.tenant.dto.UserDepartmentDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class DepartmentRemoteService {

    @Resource
    private DepartmentService departmentService;

    @Resource
    private UserTenantService userTenantService;

    public List<Long> getSubUserIdsByUserId(Long tenantId, Long userId) {
        MultiResponse<Long> res = departmentService.getDeptIdsByLeaderId(tenantId,userId);
        if(res.isSuccess()){
            MultiResponse<Long> allUserIds = departmentService.getAllUserIdByDeptIds(tenantId, res.getData(), false);
            if (allUserIds.isSuccess()) {
                return allUserIds.getData();
            }
        }
        return new ArrayList<>();
    }

    public List<UserDepartmentDTO> getSubDepartInfosByUserId(Long tenantId, Long userId) {
        MultiResponse<Long> res = departmentService.getDeptIdsByLeaderId(tenantId,userId);
        if(res.isSuccess()){
            MultiResponse<UserDepartmentDTO> response = departmentService.getAllUserDeptByDeptIds(tenantId, res.getData(), false);
            if (response.isSuccess()) {
                return response.getData();
            }
        }
        return new ArrayList<>();
    }

    public Map<Long, TenantUserInfoDTO> queryUserInfoMapByUserIds(Long tenantId, List<Long> userIds){
        SingleResponse<Map<Long, TenantUserInfoDTO>> response = userTenantService.queryMapByUserIds(tenantId, userIds);
        if(response != null && response.isSuccess()){
            return response.getData();
        }
        return new HashMap<>();
    }

    public TenantUserInfoDTO queryUserInfoByUserId(Long tenantId,Long userId){
        SingleResponse<TenantUserInfoDTO> response = userTenantService.queryByUserId(tenantId, userId);
        if(response != null && response.isSuccess()){
            return response.getData();
        }
        return null;
    }
}
