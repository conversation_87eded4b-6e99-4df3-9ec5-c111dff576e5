package com.neo.nova.app.action.actionType;/*
 *
 *          ┌─┐       ┌─┐
 *       ┌──┘ ┴───────┘ ┴──┐
 *       │                 │
 *       │       ───       │
 *       │  ─┬┘       └┬─  │
 *       │                 │
 *       │       ─┴─       │
 *       │                 │
 *       └───┐         ┌───┘
 *           │         │
 *           │         │
 *           │         │
 *           │         └──────────────┐
 *           │                        │
 *           │                        ├─┐
 *           │                        ┌─┘
 *           │                        │
 *           └─┐  ┐  ┌───────┬──┐  ┌──┘
 *             │ ─┤ ─┤       │ ─┤ ─┤
 *             └──┴──┘       └──┴──┘
 *                   Code is far away from bug with the animal protecting
 *                   神兽保佑,代码无bug
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/24 21:04
 */

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ActionSubTypeEnums {
    //    CHECK_FIX(1, "巡检");
    COMMON(0, "日常", "common"),
    CHECK_FIX(1, "巡检", "checkAndFix"),
    //    CHECK_IN_AM(1, "上午上班打卡"),
    //    CHECK_OUT_AM(2, "上午下班打卡"),
    //    CHECK_IN_PM(3, "下午上班打卡"),
    //    CHECK_OUT_PM(4, "下午下班打卡"),
    //    CHECK_IN_PHOTO(5, "拍照打卡");
    CHECK_IN_AM(2, "上午上班打卡", "checkIn"),
    CHECK_OUT_AM(3, "上午下班打卡", "checkIn"),
    CHECK_IN_PM(4, "下午上班打卡", "checkIn"),
    CHECK_OUT_PM(5, "下午下班打卡", "checkIn"),
    //    CLOTHES_PHOTO(1, "着装拍照"),
    //    GOODS_PHOTO(2, "理货拍照");
    CLOTHES_PHOTO(6, "着装拍照", "photoRecord"),
    GOODS_PHOTO(7, "理货拍照", "photoRecord"),
    //    DAY_REPORT(1, "日报"),
    //    WEEK_REPORT(2, "周报"),
    //    MONTH_REPORT(3, "月报"),
    //    YEAR_REPORT(4, "年报");
    DAY_REPORT(8, "日报", "report"),
    WEEK_REPORT(9, "周报", "report"),
    MONTH_REPORT(10, "月报", "report"),
    YEAR_REPORT(11, "年报", "report"),
    FIRST(12, "膜拜", "visitingRecord"),
    SECOND(13, "回访", "visitingRecord"),
    ;

    public static final Map<Integer, String> map = new HashMap<>();
    static {
        for (ActionSubTypeEnums value : values()) {
            map.put(value.getType(), value.getActionDesc());
        }
    }
    

    private Integer type;
    private String actionDesc;
    private String actionName;

    ActionSubTypeEnums(Integer type, String actionDesc, String actionName) {
        this.type = type;
        this.actionDesc = actionDesc;
        this.actionName = actionName;
    }

    /**
     * 通过action分类 然后通过type分类
     *
     * @return
     */
    public static Map<Integer, ActionSubTypeEnums> getByActionTypeAndType(String actionName) {
        Map<Integer, ActionSubTypeEnums> result = new HashMap<>();
        switch (actionName) {
            case "checkAndFix":
                result.put(CHECK_FIX.getType(), CHECK_FIX);
                break;
            case "checkIn":
                result.put(CHECK_IN_AM.getType(), CHECK_IN_AM);
                result.put(CHECK_IN_PM.getType(), CHECK_IN_PM);
                result.put(CHECK_OUT_AM.getType(), CHECK_OUT_AM);
                result.put(CHECK_OUT_PM.getType(), CHECK_OUT_PM);
                break;
            case "photoRecord":
                result.put(CLOTHES_PHOTO.getType(), CLOTHES_PHOTO);
                result.put(GOODS_PHOTO.getType(), GOODS_PHOTO);
                break;
            case "report":
                result.put(DAY_REPORT.getType(), DAY_REPORT);
                result.put(WEEK_REPORT.getType(), WEEK_REPORT);
                result.put(MONTH_REPORT.getType(), MONTH_REPORT);
                result.put(YEAR_REPORT.getType(), YEAR_REPORT);
        }
        return result;
    }
    

}
