package com.neo.nova.app.sync.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.sync.AbstractTableSyncConfig;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.sqlserver.SynIvPamMaterial;
import com.neo.nova.infrastructure.mapper.GoodsInfoMapper;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import jakarta.annotation.Resource;

import java.util.Objects;

public abstract class AbstractSynIvPamMaterialConfigImpl<T, R> extends AbstractTableSyncConfig<SynIvPamMaterial, GoodsInfo> {

    @Resource
    private GoodsInfoMapper goodsInfoMapper;

    @Resource
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Resource
    private UserService userService;

    @Override
    protected BaseMapper<GoodsInfo> getMysqlMapper() {
        return goodsInfoMapper;
    }

    @Override
    public String getSqlServerTableName() {
        return "syn_iv_pam_Material";
    }

    @Override
    public String getMysqlTableName() {
        return "GoodsInfo";
    }

    @Override
    public QueryWrapper<SynIvPamMaterial> getSqlServerUpdateQueryWrapper() {
        QueryWrapper<SynIvPamMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("ID");
        return null;
    }

    @Override
    public boolean needUpdate(SynIvPamMaterial sqlServerData, GoodsInfo mysqlData) {
        if (sqlServerData == null || mysqlData == null) {
            return false;
        }

        // 比较物料编号
        if (!Objects.equals(sqlServerData.getMateno() != null ? sqlServerData.getMateno().toString() : null,
                mysqlData.getCode())) {
            return true;
        }

        // 比较物料名称
        if (!Objects.equals(sqlServerData.getMatename(), mysqlData.getName())) {
            return true;
        }

        // 比较助记码
        if (!Objects.equals(sqlServerData.getMnemocode(), mysqlData.getMnemoCode())) {
            return true;
        }

        // 比较规格
        if (!Objects.equals(sqlServerData.getSpec(), mysqlData.getSpec())) {
            return true;
        }

        // 比较单位
        if (!Objects.equals(sqlServerData.getUnit(), mysqlData.getUnit())) {
            return true;
        }

        // 比较净重
        if (!Objects.equals(sqlServerData.getJweight(), mysqlData.getJWeight())) {
            return true;
        }

        // 比较毛重
        if (!Objects.equals(sqlServerData.getMweight(), mysqlData.getMWeight())) {
            return true;
        }

        // 比较状态
        if (!Objects.equals(sqlServerData.getUstate(), mysqlData.getStatus())) {
            return true;
        }

        // 如果MySQL数据有更新时间，可以比较更新时间
        if (mysqlData.getUpdated() != null) {
            // 这里可以根据业务需求决定是否需要时间比较
            // 由于SQL Server数据没有明确的更新时间字段，暂时不做时间比较
        }

        return false;
    }

    @Override
    public Object getMysqlPrimaryKeyValue(GoodsInfo data) {
        return data.getOutId();
    }

    @Override
    public Object getSqlServerPrimaryKeyValue(SynIvPamMaterial data) {
        return data.getId();
    }

    @Override
    public GoodsInfo convertToMysqlEntity(SynIvPamMaterial data) {
        return fromMaterial(data);
    }

    /**
     * SynIvPamMaterial转GoodsInfo
     *
     * @param material
     * @return
     */
    public GoodsInfo fromMaterial(SynIvPamMaterial material) {
        if (material == null) {
            return null;
        }

        GoodsInfo goodsInfo = new GoodsInfo();

        // 基本信息映射
        goodsInfo.setOutId(String.valueOf(material.getId()));
        goodsInfo.setOutType(getSqlserverSource().getValue());

        goodsInfo.setCode(String.valueOf(material.getMateno()));
        goodsInfo.setName(material.getMatename());
        goodsInfo.setMnemoCode(material.getMnemocode());
        goodsInfo.setSpec(material.getSpec());
        goodsInfo.setUnit(material.getUnit());
        goodsInfo.setJWeight(material.getJweight());
        goodsInfo.setMWeight(material.getMweight());

        // 关联ID映射
        BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
        param.setBusinessDomain(21);
        switch (getSqlserverSource()) {
            case SQLSERVER1:
                param.setOutId1(String.valueOf(material.getMatetypeid() != null ? material.getMatetypeid().longValue() : null));
                break;
            case SQLSERVER2:
                param.setOutId2(String.valueOf(material.getMatetypeid() != null ? material.getMatetypeid().longValue() : null));
                break;
        }
        MultiResponse<TagLeafInfoDto> queryResult = businessTreeTagReadReadService.queryGoodsCategorySetting(param, new BaseTagQueryOption());
        if (queryResult.isSuccess() && queryResult.getData() != null) {
            TagLeafInfoDto tagLeafInfoDto = queryResult.getData().get(0);
            goodsInfo.setGoodsTypeId(tagLeafInfoDto.getId());
        }

        if (material.getShipperid() != null) {
            SingleResponse<UserInfoDTO> userInfoDTO = userService.queryByUserThirdParty(String.valueOf(material.getShipperid()), ThirdPartyEnum.OUT1.getCode(), "ww9c836769f4ec7e7d");
            if (userInfoDTO.isSuccess()) {
                if (userInfoDTO.getData() != null) {
                    goodsInfo.setShipperId(userInfoDTO.getData().getUserId());
                } else {
                    goodsInfo.setShipperId(-1L);
                }
            }
        }

        // 状态映射
        goodsInfo.setStatus(material.getUstate() != null && material.getUstate() == 1 ? 1 : 0);
        goodsInfo.setIsDeleted(0);

        // 设置默认值
        goodsInfo.setTenantId(21L); // 默认租户ID
        goodsInfo.setCreated(System.currentTimeMillis());
        goodsInfo.setUpdated(System.currentTimeMillis());

        return goodsInfo;
    }

    @Resource
    private MetricService metricService;

    @Override
    public void after() {
        //标签同步完 通知下游
        metricService.init(21L);
    }
}
