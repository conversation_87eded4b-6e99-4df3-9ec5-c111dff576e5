package com.neo.nova.app.service;

import com.neo.api.Response;
import com.neo.nova.app.request.DataSourceUploadRequest;
import com.neo.nova.app.vo.PendingDataResponse;
import com.neo.nova.app.vo.PendingQueryVO;
import com.neo.nova.domain.entity.ExcelDataProcessed;

import java.util.List;

/**
 * Excel数据处理服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface ExcelDataProcessService {

    /**
     * 上传并处理Excel文件
     *
     * @param request      DataSourceUploadRequest
     * @param dataSourceId 数据源ID
     * @return 处理结果
     */
    Response processExcel(DataSourceUploadRequest request, Long dataSourceId);

    /**
     * 异步上传并处理Excel文件
     *
     * @param request      DataSourceUploadRequest
     * @param dataSourceId 数据源ID
     */
    void processExcelAsync(DataSourceUploadRequest request, Long dataSourceId);


    void processAndSave(Long tenantId, List<ExcelDataProcessed> originalDataList);
    /**
     * 获取待确认的数据列表和对应的数据源配置信息
     *
     * @return 待确认的数据和数据源配置信息
     */
    PendingDataResponse getPendingDataWithConfig(PendingQueryVO pendingQueryVO);

    /**
     * 更新处理后的数据
     *
     * @return 更新结果
     */
    Response updateProcessedData(Long userId, Long dataSourceId, List<Long> ids, ExcelDataProcessed update);

    /**
     * 确认保存数据到统计表
     *
     * @param dataSourceId 处理后数据ID列表
     * @return 保存结果
     */
    Response confirmAndSaveData(Long userId, Long tenantId, Long dataSourceId,String remark);
}
