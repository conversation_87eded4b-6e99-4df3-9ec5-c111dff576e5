package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("SalesUser")
public class SalesUser {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenantId")
    private Long tenantId;

    /**
     * 用户id
     */
    @TableField("userId")
    private Long userId;

    /**
     * 部门id
     */
    @TableField("deptId")
    private Long deptId;

    /**
     * 客户id
     */
    @TableField("customerId")
    private Long customerId;

    /**
     * 客户名
     */
    @TableField("customerName")
    private String customerName;

    /**
     * 月完成度
     */
    @TableField("monthRate")
    private BigDecimal monthRate;

    /**
     * 年完成度
     */
    @TableField("yearRate")
    private BigDecimal yearRate;

    /**
     * 创建时间戳
     */
    @TableField("created")
    private Long created;

    /**
     * 更新时间戳
     */
    @TableField("updated")
    private Long updated;

    /**
     * 0在职,1离职
     */
    @TableField("isDeleted")
    private Integer isDeleted;

}
