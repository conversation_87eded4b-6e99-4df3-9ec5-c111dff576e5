package com.neo.nova.domain.dto;

import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.MetricDataTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/30
 **/
@Data
public class PerformanceTargetTreeDTO {

    private Long id;
    /**
     * 计划id
     */
    private Long planId;
    /**
     * 父目标id
     */
    private Long parentId;
    /**
     * 根目标id
     */
    private Long rootId;
    /**
     * 目标名称
     */
    private String targetName;
    /**
     * 目标id
     */
    private String targetId;
    /**
     * @see MetricCodeEnum
     * 指标编码
     */
    private String metricCode;
    /**
     * 责任人Id
     */
    private Long ownerId;
    /**
     * 责任人
     */
    private String ownerName;
    /**
     * @see MetricDataTypeEnum
     */
    private Integer metricDataType;
    /**
     * @see MetricDataTypeEnum
     */
    private String metricName;
    /**
     * 单位
     */
    private String unit;
    /**
     * 目标值
     */
    private BigDecimal targetValue;
    /**
     * 目标占比
     */
    private BigDecimal targetPercentShare;
    /**
     * 环比
     */
    private BigDecimal momValue;
    /**
     * 环比占比
     */
    private BigDecimal momPercentShare;
    /**
     * 同比
     */
    private BigDecimal yoyValue;
    /**
     * 同比占比
     */
    private BigDecimal yoyPercentShare;
    /**
     * 实际值
     */
    private BigDecimal actualValue;
    /**
     * 实际值展示
     */
    private String actualValueShow;
    /**
     * 实际占比
     */
    private BigDecimal actualPercentShare;

    /**
     * 子节点
     */
    private List<PerformanceTargetTreeDTO> children;


}
