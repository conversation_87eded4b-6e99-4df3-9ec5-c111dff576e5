package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @TableName syn_iv_pam_Material
 */
@TableName(value = "syn_iv_pam_Material")
@Data
public class SynIvPamMaterial {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "MateNo")
    private Integer mateno;

    /**
     *
     */
    @TableField(value = "MateName")
    private String matename;

    /**
     *
     */
    @TableField(value = "MnemoCode")
    private String mnemocode;

    /**
     *
     */
    @TableField(value = "OrderCode")
    private Integer ordercode;

    /**
     *
     */
    @TableField(value = "MateTypeID")
    private Integer matetypeid;

    /**
     *
     */
    @TableField(value = "ShipperID")
    private Integer shipperid;

    /**
     *
     */
    @TableField(value = "Spec")
    private String spec;

    /**
     *
     */
    @TableField(value = "Unit")
    private String unit;

    /**
     *
     */
    @TableField(value = "JWeight")
    private BigDecimal jweight;

    /**
     *
     */
    @TableField(value = "MWeight")
    private BigDecimal mweight;

    /**
     *
     */
    @TableField(value = "UState")
    private Integer ustate;
}