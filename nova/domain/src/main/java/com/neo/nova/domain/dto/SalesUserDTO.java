package com.neo.nova.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SalesUserDTO {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */

    private String deptName;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 月完成度
     */
    private BigDecimal monthRate;

    /**
     * 年完成度
     */
    private BigDecimal yearRate;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 更新时间戳
     */
    private Long updated;

    /**
     * 0在职,1离职
     */
    private Integer isDeleted;
}
