package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 货品信息表
 * @TableName GoodsInfo
 */
@TableName(value ="GoodsInfo")
@Data
public class GoodsInfo {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 货品编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 三方客户ID
     */
    @TableField(value = "outId")
    private String outId;

    /**
     * 三方类型
     */
    @TableField(value = "outType")
    private String outType;

    /**
     * 货品名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 货品名称缩写
     */
    @TableField(value = "mnemoCode")
    private String mnemoCode;

    /**
     * 货品类型id
     */
    @TableField(value = "goodsTypeId")
    private Long goodsTypeId;

    /**
     * 发货员id
     */
    @TableField(value = "shipperId")
    private Long shipperId;

    /**
     * 货品规格
     */
    @TableField(value = "spec")
    private String spec;

    /**
     * 货品单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 净重
     */
    @TableField(value = "jWeight")
    private BigDecimal jWeight;

    /**
     * 毛重
     */
    @TableField(value = "mWeight")
    private BigDecimal mWeight;

    /**
     * 状态 0休眠 1使用 2停用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
