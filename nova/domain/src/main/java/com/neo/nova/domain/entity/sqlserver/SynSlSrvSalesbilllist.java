package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @TableName syn_sl_srv_SalesBillList
 */
@TableName(value = "syn_sl_srv_SalesBillList")
@Data
public class SynSlSrvSalesbilllist {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @TableField(value = "MateID")
    private Integer mateid;

    /**
     *
     */
    @TableField(value = "SellPrice")
    private BigDecimal sellprice;

    /**
     *
     */
    @TableField(value = "Qty")
    private BigDecimal qty;

    /**
     *
     */
    @TableField(value = "DelivQty")
    private BigDecimal delivqty;

    /**
     *
     */
    @TableField(value = "BillMoney")
    private BigDecimal billmoney;

    /**
     *
     */
    @TableField(value = "DelivMoney")
    private BigDecimal delivmoney;

    /**
     *
     */
    @TableField(value = "SalesBillID")
    private Integer salesbillid;
}