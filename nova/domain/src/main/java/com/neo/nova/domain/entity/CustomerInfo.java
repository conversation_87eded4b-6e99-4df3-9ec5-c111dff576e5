package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 客户信息表
 *
 * @TableName CustomerInfo
 */
@TableName(value = "CustomerInfo")
@Data
public class CustomerInfo {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 客户编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 三方客户ID
     */
    @TableField(value = "outId")
    private String outId;

    /**
     * 三方类型
     */
    @TableField(value = "outType")
    private String outType;

    /**
     * 价格类型
     */
    @TableField(value = "priceTypeId")
    private Long priceTypeId;
    
    /**
     * 客户名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 客户渠道
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 大区ID
     */
    @TableField(value = "supermarketAreaId")
    private String supermarketAreaId;

    /**
     * 客户名称缩写
     */
    @TableField(value = "mnemoCode")
    private String mnemoCode;

    /**
     * 客户联系人
     */
    @TableField(value = "linkMan")
    private String linkMan;
    /**
     * 职位
     */
    @TableField(value = "linkPosition")
    private String linkPosition;





    /**
     * 客户联系号码
     */
    @TableField(value = "contractNumber")
    private String contractNumber;

    /**
     * 客户收货地址
     */
    @TableField(value = "deliveryAddress")
    private String deliveryAddress;

    /**
     * 客户类型id
     */
    @TableField(value = "customerTypeId")
    private Long customerTypeId;

    /**
     * 客户线路id
     */
    @TableField(value = "customerLineId")
    private Long customerLineId;

    /**
     * 行政区域id
     */
    @TableField(value = "adminRegionId")
    private Long adminRegionId;

    /**
     * 销售区域id
     */
    @TableField(value = "customerAreaId")
    private Long customerAreaId;

    /**
     * 开发日期
     */
    @TableField(value = "openDate")
    private Long openDate;

    /**
     * 审核状态
     */
    @TableField(value = "auditFlag")
    private Integer auditFlag;

    /**
     * 客户等级
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 业务员Id
     */
    @TableField(value = "salesId")
    private Long salesId;

    /**
     * 状态 0正常1停用2休眠
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 价格显示 0不显示 1显示价格 2显示零售价
     */
    @TableField(value = "dispPrice")
    private Integer dispPrice;

    /**
     * 创建人
     */
    @TableField(value = "createdBy")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新人
     */
    @TableField(value = "updatedBy")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
