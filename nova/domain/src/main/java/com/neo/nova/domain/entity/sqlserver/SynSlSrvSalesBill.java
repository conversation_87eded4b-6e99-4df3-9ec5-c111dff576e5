package com.neo.nova.domain.entity.sqlserver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("syn_sl_srv_SalesBill") // 对应数据库表名
public class SynSlSrvSalesBill {

    @TableId(value = "id", type = IdType.AUTO)
    // 对应 id 列，int 类型
    private Integer id;

    @TableField("BILLNO")
    // 对应 BILLNO 列，varchar(20)
    private String billno;

    @TableField("BILLTYPE")
    // 对应 BILLTYPE 列，char(1)
    private String billtype;

    @TableField("CUSTID")
    // 对应 CUSTID 列，int
    private Integer custid;

    @TableField("DELIVYDATE")
    // 对应 DELIVDATE 列，datetime
    private Date delivdate;

    @TableField("BILLSTATE")
    // 对应 BILLSTATE 列，char(1)
    private String billstate;

    @TableField("SYNID")
    // 对应 SYNID 列，int
    private Integer synid;

    @TableField("PRINTTIME")
    // 对应 PRINTTIME 列，int
    private Integer printtime;

    @TableField("BILLFROM")
    // 对应 BILLFROM 列，char(1)
    private String billfrom;

    @TableField("BILLDATE")
    // 对应 BILLDATE 列，datetime
    private Date billdate;

    @TableField("BILLMONEY")
    // 对应 BILLMONEY 列，numeric(14,2)
    private BigDecimal billmoney;

    @TableField("VerifyPerson")
    // 对应 VerifyPerson 列，varchar(200)
    private String verifyperson;

    @TableField("VerifyDate")
    // 对应 VerifyDate 列，datetime
    private Date verifydate;
}




