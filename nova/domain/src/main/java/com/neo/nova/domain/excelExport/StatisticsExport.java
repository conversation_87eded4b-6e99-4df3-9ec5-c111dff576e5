package com.neo.nova.domain.excelExport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class StatisticsExport {

    @ExcelProperty(value = "序号", index = 0)
    private Long id;

    @ExcelProperty(value = "产品名称", index = 1)
    private String produceName;

    @ExcelProperty(value = "产品编号", index = 2)
    private String produceCode;

    @ExcelProperty(value = "客户名称", index = 3)
    private String customerName;

    @ExcelProperty(value = "客户编号", index = 4)
    private String customerCode;

    @ExcelProperty(value = "客户等级", index = 5)
    private Integer customerLevel;

    @ExcelProperty(value = "业务员姓名", index = 6)
    private String salesName;

    @ExcelProperty(value = "业务员编号", index = 7)
    private String salesCode;

    @ExcelProperty(value = "渠道", index = 8)
    private String channel;

    @ExcelProperty(value = "门店类型", index = 9)
    private String customerType;

    @ExcelProperty(value = "销售额", index = 10)
    private Long amount;

    @ExcelProperty(value = "统计日期", index = 11)
    private String visitDate;

    @ExcelIgnore
    private Long tenantId;

    @ExcelIgnore
    private Long produceId;

    @ExcelIgnore
    private Long produceTypeId;

    @ExcelIgnore
    private Long customerId;

    @ExcelIgnore
    private String customerMnemoCode;

    @ExcelIgnore
    private Long customerTypeId;

    @ExcelIgnore
    private Long supermarketAreaId;

    @ExcelIgnore
    private Long salesRegionId;

    @ExcelIgnore
    private String adminRegionId;

    @ExcelIgnore
    private Long salesId;

    // 中文名称字段
    @ExcelIgnore
    private String channelName;

    @ExcelIgnore
    private String supermarketAreaName;

    @ExcelIgnore
    private String adminRegionName;

    @ExcelIgnore
    private String customerLevelName;

    @ExcelIgnore
    private String produceTypeName;
}
