package com.neo.nova.domain.dto;

import com.neo.nova.domain.entity.WorkOrder;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 15:25
 */
@Data
@Builder
public class WorkOrderDto {
    /**
     * 主键
     */

    private Long id;

    /**
     * 创建人id
     */

    private Long creatorId;

    /**
     * 执行人id
     */

    private Long executorId;

    /**
     * 更新人
     */

    private Long updaterId;

    /**
     * 任务描述
     */

    private String description;

    /**
     * 状态
     */

    private Integer status;

    /**
     * 工单 优先级  一般  紧急
     */

    private Integer priority;

    /**
     * 工单类型 日常工单 手工创建
     */

    private Integer type;

    /**
     * 工单开始时间
     */

    private LocalDateTime workOrderStartTime;

    /**
     * 结束时间
     */

    private LocalDateTime workOrderEndTime;

    /**
     * 创建时间
     */

    private LocalDateTime created;

    /**
     * 更新时间
     */

    private LocalDateTime updated;

    /**
     * 是否删除 0 否 1 是
     */

    private Integer deleted;
 
    public static WorkOrderDto covertDto(WorkOrder workOrder) {
        if (workOrder == null) {
            return null;
        }
        return WorkOrderDto.builder()
                .id(workOrder.getId())
                .creatorId(workOrder.getCreatorId())
                .executorId(workOrder.getExecutorId())
                .updaterId(workOrder.getUpdaterId())
                .description(workOrder.getDescription())
                .status(workOrder.getStatus())
                .priority(workOrder.getPriority())
                .type(workOrder.getType())
                .workOrderStartTime(workOrder.getWorkOrderStartTime())
                .workOrderEndTime(workOrder.getWorkOrderEndTime())
                .created(workOrder.getCreated())
                .updated(workOrder.getUpdated()).build();
    }


}
