package com.neo.nova.adapter.controller;

import com.neo.api.MultiResponse;
import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.nova.domain.dto.PerformanceTargetTreeDTO;
import com.neo.nova.app.request.PerformanceCalcRequest;
import com.neo.nova.app.request.PerformanceCreateRequest;
import com.neo.nova.app.request.PerformanceDeleteRequest;
import com.neo.nova.app.request.PerformanceListRequest;
import com.neo.nova.app.service.PerformanceService;
import com.neo.nova.app.vo.PerformanceListVO;
import com.neo.nova.app.validator.PerformanceValidationMessages;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;


/**
 * 绩效管理
 *
 * <AUTHOR>
 * @since 2025/6/30
 **/
@RestController
@RequestMapping("/performance")
public class PerformanceController {

    @Resource
    private PerformanceService performanceService;

    /**
     * 查询业绩目标
     */
    @GetMapping("list")
    @NeedLogin
    public SingleResponse<PerformanceListVO> list(PerformanceListRequest performanceListRequest) {
        performanceListRequest.setUserId(SessionContextHolder.getUserId());
        performanceListRequest.setTenantId(SessionContextHolder.getTenantId());
        return SingleResponse.of(performanceService.list(performanceListRequest));
    }

    /**
     * 创建业绩目标
     */
    @PostMapping("batchAddOrModify")
    @NeedLogin
    public Response batchAddOrModify(@RequestBody PerformanceCreateRequest performanceCreateRequest) {
        performanceCreateRequest.setUserId(SessionContextHolder.getUserId());
        performanceCreateRequest.setTenantId(SessionContextHolder.getTenantId());
        Boolean result = performanceService.batchAddOrModify(performanceCreateRequest);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_OPERATION_FAILED", PerformanceValidationMessages.OPERATION_FAILED);
        }

    }

    /**
     * 批量试算业绩目标
     */
    @PostMapping("preCalc")
    @NeedLogin
    public MultiResponse<PerformanceTargetTreeDTO> preCalc(@RequestBody PerformanceCalcRequest performanceCalcRequest) {
        performanceCalcRequest.setUserId(SessionContextHolder.getUserId());
        performanceCalcRequest.setTenantId(SessionContextHolder.getTenantId());
        return MultiResponse.of(performanceService.preCalc(performanceCalcRequest));
    }

    /**
     * 删除计划及其所有目标
     */
    @PostMapping("deletePlan")
    @NeedLogin
    public Response deletePlan(@RequestBody PerformanceDeleteRequest performanceDeleteRequest) {
        performanceDeleteRequest.setUserId(SessionContextHolder.getUserId());
        performanceDeleteRequest.setTenantId(SessionContextHolder.getTenantId());
        Boolean result = performanceService.deletePlan(performanceDeleteRequest);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_DELETE_FAILED", "计划删除失败");
        }
    }

    /**
     * 删除目标
     */
    @PostMapping("deleteTarget")
    @NeedLogin
    public Response deleteTarget(@RequestBody PerformanceDeleteRequest performanceDeleteRequest) {
        performanceDeleteRequest.setUserId(SessionContextHolder.getUserId());
        performanceDeleteRequest.setTenantId(SessionContextHolder.getTenantId());
        Boolean result = performanceService.deleteTarget(performanceDeleteRequest);
        if (Boolean.TRUE.equals(result)) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("PERFORMANCE_DELETE_FAILED", "目标删除失败");
        }
    }


}
