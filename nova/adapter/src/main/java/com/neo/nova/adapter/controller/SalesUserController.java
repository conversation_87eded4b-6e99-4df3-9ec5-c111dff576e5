package com.neo.nova.adapter.controller;

import com.google.common.collect.Lists;
import com.neo.api.PageResponse;
import com.neo.nova.app.service.SalesUserService;
import com.neo.nova.app.vo.CustomerQueryVO;
import com.neo.nova.app.vo.SalesUserQueryVO;
import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.dto.CustomerInfoDTO;
import com.neo.nova.domain.dto.SalesUserDTO;
import com.neo.nova.domain.dto.SalesUserInfoDTO;
import com.neo.session.SessionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/salesuser")
public class SalesUserController {
    @Autowired
    private SalesUserService salesUserService;
    /**
     * 获取销售员列表
     * @param salesUserQueryVO
     * @return
     */

    @PostMapping("/list")

    @ResponseBody
    public PageResponse<SalesUserDTO> list(@RequestBody(required = false)SalesUserQueryVO salesUserQueryVO) {

        SalesUserInfoDTO salesUserInfoDTO =salesUserService.list(salesUserQueryVO);
        return PageResponse.of(salesUserInfoDTO.getSalesUserDTOS(), salesUserInfoDTO.getTotalCount(), salesUserQueryVO.getPageSize(), salesUserQueryVO.getPageIndex());

    }


}
