package com.neo.nova.adapter.controller;

import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.action.ActionFactory;
import com.neo.nova.app.action.actionType.ActionSubTypeEnums;
import com.neo.nova.app.action.config.ActionConfig;
import com.neo.nova.app.action.config.RoleConfig;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.EventEnums;
import com.neo.nova.app.action.enums.RoleEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.service.CustomerRemarkService;
import com.neo.nova.app.service.WorkOrderEventService;
import com.neo.nova.app.service.WorkOrderOperationService;
import com.neo.nova.app.vo.CheckInDetailResponse;
import com.neo.nova.app.vo.SimpleDetailResponse;
import com.neo.nova.app.vo.WorkOrderDetailResponse;
import com.neo.nova.app.vo.WorkOrderListItemDTO;
import com.neo.nova.app.vo.WorkOrderQueryRequest;
import com.neo.nova.app.vo.WorkOrderSubTypeResponse;
import com.neo.nova.app.vo.WorkOrderTypeResponse;
import com.neo.nova.domain.dto.WorkOrderDetailDto;
import com.neo.nova.domain.dto.WorkOrderDto;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 *
 * 用途说明: 工单表的接口
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 14:42
 */
@RequestMapping("/workOrder")
@RestController
@Slf4j
public class WorkOrderController {

    @Resource
    private WorkOrderOperationService workOrderOperationService;
    @Resource
    private WorkOrderEventService workOrderEventService;

    @Resource
    private CustomerRemarkService customerRemarkService;


    /**
     * 执行任务
     *
     * @param actionBaseModel
     * @return
     */
    @NeedLogin
    @PostMapping("/execute")
    public Boolean execute(@RequestBody ActionBaseModel actionBaseModel) {

        actionBaseModel.setUserId(SessionContextHolder.getUserId());
        WorkOrderDto workOrderById = workOrderOperationService.getWorkOrderById(actionBaseModel.getWorkOrderId());
        if (workOrderById == null) {
            log.error("工单不存在 {}", actionBaseModel);
            return false;
        }

//        WorkOrderDetailDto workOrderDetailById = workOrderOperationService.getWorkOrderDetailById(actionBaseModel.getWorkOrderDetailId());
//        if (workOrderDetailById == null) {
//            log.error("工单详情不存在 {}", actionBaseModel);
//            return false;
//        }

        actionBaseModel.setActionName(ActionRelationEnums.actionMap.get(workOrderById.getType()).getActionName());
//        Integer typeByActionName = ActionRelationEnums.getTypeByActionName(workOrderDetailById.getAction());
//        if (typeByActionName == null) {
//            log.error("工单类型不匹配 {}", actionBaseModel);
//            return false;
//        }

//        Map<Integer, ActionSubTypeEnums> byActionTypeAndType = ActionSubTypeEnums.getByActionTypeAndType(workOrderDetailById.getAction());
//        if (byActionTypeAndType.isEmpty()) {
//            log.error("工单类型不匹配 {}", actionBaseModel);
//            return false;
//        }

        // 创建处理工单事件
        workOrderEventService.createWorkOrderEvent(actionBaseModel.getWorkOrderId(),
                actionBaseModel.getWorkOrderDetailId(),
                actionBaseModel.getUserId(),
                EventEnums.PROCESS.getEventType(),
                EventEnums.PROCESS.getEventDesc()
        );



        return ActionFactory.execute(actionBaseModel);
    }

    /**
     * 创建任务
     *
     * @param actionTaskModel
     * @return
     */
    @PostMapping("/create")
    @NeedLogin
    public SingleResponse<Boolean> create(@RequestBody ActionTaskModel actionTaskModel) {
        actionTaskModel.setTenantId(SessionContextHolder.getTenantId());
        boolean result = false;
        if(actionTaskModel.getActionType() == ActionRelationEnums.CHECK_IN.getActionType()){
            RoleEnums byCode = RoleEnums.CUSTOMER_SALESMAN_DIRECTOR;
            Map<RoleEnums, List<ActionConfig>> actionRoleConfig = RoleConfig.actionRoleConfig;
            List<ActionConfig> actionConfig = actionRoleConfig.get(byCode);
            actionTaskModel = new ActionTaskModel();
            actionTaskModel.setUserId(SessionContextHolder.getUserId());
            actionTaskModel.setPriority(WorkOrderPriorityEnums.NORMAL.getCode());
            Long workOrderId = ActionFactory.initWorkOrder(actionTaskModel);
            for (ActionConfig value : actionConfig) {
                if (value == null) {
                    continue;
                }
                actionTaskModel.setActionName(value.getAction().getActionName());
                actionTaskModel.setWorkOrderDetailType(value.getType());
                actionTaskModel.setSorted(value.getSorted());
                if (workOrderId == null) {
                    // 创建失败子工单 继续下一个创建
                    log.error("初始化工单失败 request->{} userId->{} ", actionTaskModel);
                    continue;
                }
                actionTaskModel.setWorkOrderId(workOrderId);
                result = value.getAction().createTask(actionTaskModel);
            }
        }else{
            result = ActionFactory.createTask(actionTaskModel);
        }
        return SingleResponse.buildSuccess(result);
    }

    /**
     * 更新任务
     *
     * @param actionTaskModel
     * @return
     */
    @PostMapping("/update")
    @NeedLogin
    public SingleResponse<Boolean> update(@RequestBody ActionTaskModel actionTaskModel) {
        actionTaskModel.setTenantId(SessionContextHolder.getTenantId());
        return workOrderOperationService.updateWorkOrderAndDetail(actionTaskModel);
    }

    /**
     * 查询工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @PostMapping("/query")
    public PageResponse<WorkOrderListItemDTO> queryWorkOrders(@Valid @RequestBody WorkOrderQueryRequest request) {
        request.setExecutorId(null);
        return workOrderOperationService.queryWorkOrders(request);
    }

    /**
     * 查询我的工单列表
     *
     * @param request 查询请求参数
     * @return 工单列表响应
     */
    @PostMapping("/querymine")
    @NeedLogin
    public PageResponse<WorkOrderListItemDTO> queryMyWorkOrders(@Valid @RequestBody WorkOrderQueryRequest request) {
        request.setExecutorId(SessionContextHolder.getUserId());
        return workOrderOperationService.queryWorkOrders(request);
    }

    /**
     * 查询工单详情
     *
     * @param workOrderId 工单ID
     * @return 工单详情响应
     */
    @GetMapping("/detail/{workOrderId}")
    public SingleResponse<WorkOrderDetailResponse> queryOrderDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.getWorkOrderDetail(workOrderId);
    }

    /**
     * 查询简单工单详情，就是每个子tab的详情
     *
     * @param workOrderId 工单ID
     * @return 详情响应
     */
    @GetMapping("/simple/detail/{workOrderId}")
    public SingleResponse<SimpleDetailResponse> querySimpleDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.getVisitDetail(workOrderId);
    }


    /**
     * 查询导购日常打卡详情
     *
     * @param workOrderId 工单ID
     * @return 打卡详情响应
     */
    @GetMapping("/checkin/detail/{workOrderId}")
    public SingleResponse<CheckInDetailResponse> queryCheckInDetail(@PathVariable Long workOrderId) {
        return workOrderOperationService.getCheckInDetail(workOrderId);
    }

    /**
     * 获取工单类型列表
     *
     * @return 工单类型响应
     */
    @GetMapping("/workordertypes")
    public SingleResponse<WorkOrderTypeResponse> getWorkOrderTypes() {
        return workOrderOperationService.getWorkOrderTypes();
    }

    /**
     * 根据工单类型获取子类型列表
     *
     * @param actionName 工单类型名称
     * @return 工单子类型响应
     */
    @GetMapping("/subtypes/{actionName}")
    public SingleResponse<WorkOrderSubTypeResponse> getWorkOrderSubTypes(@PathVariable String actionName) {
        return workOrderOperationService.getWorkOrderSubTypes(actionName);
    }


}
