package com.neo.nova.adapter.controller;

import com.google.common.collect.Lists;
import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.Response;
import com.neo.api.SingleResponse;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.app.vo.CustomerDetailVo;
import com.neo.nova.app.vo.CustomerLevelVO;
import com.neo.nova.app.vo.CustomerQueryVO;
import com.neo.nova.app.vo.MetricCodeVO;
import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.dto.CustomerInfoDTO;
import com.neo.nova.domain.enums.CustomerLevelEnum;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.session.SessionContextHolder;
import com.neo.session.annotation.NeedLogin;
import org.checkerframework.checker.units.qual.N;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @since 2025/7/8
 **/
@RestController
@RequestMapping("/customer")

public class CustomerController {
    @Autowired
    private CustomerService customerService;

    /**
     * 客户信息查询
     *
     * @param
     * @return
     */
    @PostMapping("/list")

    @ResponseBody
    public PageResponse<CustomerDTO> list(@RequestBody(required = false) CustomerQueryVO customerQueryVO) {
        long userId = SessionContextHolder.getUserId();
        customerQueryVO.setTenantId(21L);
        if(customerQueryVO.getStatus() == null){
            customerQueryVO.setStatus(Lists.newArrayList(1));
        }
        CustomerInfoDTO customerInfoDTO = customerService.list(customerQueryVO);
        return PageResponse.of(customerInfoDTO.getCustomerInfos(), customerInfoDTO.getTotalCount(), customerQueryVO.getPageSize(), customerQueryVO.getPageIndex());

    }

    /**
     * 我的客户信息查询
     *
     * @param
     * @return
     */
    @PostMapping("/mylist")

    public PageResponse<CustomerDTO> myList(@RequestBody CustomerQueryVO customerQueryVO) {

        CustomerInfoDTO customerInfoDTO = customerService.myList(customerQueryVO);
        return PageResponse.of(customerInfoDTO.getCustomerInfos(), customerInfoDTO.getTotalCount(), customerQueryVO.getPageSize(), customerQueryVO.getPageIndex());

    }

    /**
     * 公海客户信息查询
     *
     * @param
     * @return
     */
    @PostMapping("/highseaslist")

    public PageResponse<CustomerDTO> highSeasList(@RequestBody CustomerQueryVO customerQueryVO) {

        CustomerInfoDTO customerInfoDTO = customerService.highSeasList(customerQueryVO);
        return PageResponse.of(customerInfoDTO.getCustomerInfos(), customerInfoDTO.getTotalCount(), customerQueryVO.getPageSize(), customerQueryVO.getPageIndex());
    }


    /**
     * 获取客户类型枚举
     *
     * @return
     */
    @RequestMapping("/listCustomerLevel")
    @NeedLogin
    public MultiResponse<CustomerLevelVO> listCustomerLevel() {
        return MultiResponse.of(Lists.newArrayList(CustomerLevelEnum.LEVEL_A, CustomerLevelEnum.LEVEL_B, CustomerLevelEnum.LEVEL_C, CustomerLevelEnum.LEVEL_D)
                .stream().map(CustomerLevelVO::new).toList());
    }

    /**
     * 用户详情
     * @param customerId
     * @return
     */
    @RequestMapping("/customerDetail")
    public SingleResponse<CustomerDetailVo> getCustomerDetail(Long customerId) {
        CustomerDetailVo customerDetailVo  = customerService.getCustomerDetail(customerId);
        return  SingleResponse.of(customerDetailVo);

    }

    /**
     * 新增和编辑公海客户
     *
     * @param customerDTO 客户信息
     * @return 操作结果
     */
    @PostMapping("/addAndUpdateCustomer")
    @NeedLogin
    public Response addAndUpdateCustomer(@RequestBody CustomerDTO customerDTO) {
        Boolean result = customerService.addAndUpdateCustomer(customerDTO);
        if (result) {
            return Response.buildSuccess();
        } else {
            return Response.buildFailure("操作失败", "保存客户信息失败");
        }
    }
}
