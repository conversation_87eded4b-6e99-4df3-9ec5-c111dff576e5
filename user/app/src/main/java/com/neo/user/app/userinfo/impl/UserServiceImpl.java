package com.neo.user.app.userinfo.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.tennant.ThirdPartyAppInfoCacheManager;
import com.neo.user.app.userinfo.converter.UsersConverter;
import com.neo.user.app.userinfo.converter.UsersThirdPartyConverter;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.ThirdPartyInfoDTO;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.entity.UserMobile;
import com.neo.user.domain.entity.UserThirdParty;
import com.neo.user.client.enums.ThirdPartyEnum;
import com.neo.user.client.enums.UserGenderEnum;
import com.neo.user.client.enums.UserStatusEnum;
import com.neo.user.domain.gateway.IUserMobileRepository;
import com.neo.user.domain.gateway.IUserRepository;
import com.neo.user.domain.gateway.IUserThirdPartyRepository;
import com.neo.user.domain.gateway.cache.EnumThirdPartyAppInfoCacheService;
import com.neo.user.domain.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserServiceImpl implements UserService{

    @Autowired
    private IUserRepository iUserRepository;

    @Autowired
    private IUserMobileRepository iUserMobileRepository;

    @Autowired
    private IUserThirdPartyRepository iUserThirdPartyRepository;

    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    @Autowired
    private ThirdPartyAppInfoCacheManager thirdPartyAppInfoCacheManager;


    public SingleResponse<UserInfoDTO> queryUserByUserId(Long userId) {
        SingleResponse<UserInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (userId <= 0) {
            log.error("[UserServiceImpl.queryUserByUserId][invalid params][userId={}]", userId);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.eq("userId", userId);
            Optional<User> user = iUserRepository.getOneOpt(userWrapper, Boolean.TRUE);
            if (user.isEmpty()){
                singleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
                singleResponse.setErrMessage("user not found error");
                return singleResponse;
            }
            // 组装VO对象返回
            singleResponse.setSuccess(Boolean.TRUE);
            singleResponse.setData(UsersConverter.doToVO(user.get()));
            return singleResponse;

        } catch (Exception e) {
            log.error("[UserServiceImpl.queryUserByUserId][system failed][userId=" + userId + "]", e);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public MultiResponse<UserInfoDTO> queryByUserIds(List<Long> userIds) {
        MultiResponse<UserInfoDTO> mutilResponse = new MultiResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(userIds) || userIds.size() > 500) {
            log.error("[UserServiceImpl.queryByUserIds][invalid params][userIds={}]", userIds);
            mutilResponse.setErrCode(UserConstants.ERROR_PARAM);
            mutilResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return mutilResponse;
        }
        try {
            // 从DB中查询数据
            userIds = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());


            List<User> users = iUserRepository.listByIds(userIds);
            mutilResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(users)) {
                // 未查到数据
                return mutilResponse;
            }
            // 组装VO对象返回
            List<UserInfoDTO> values = new ArrayList<UserInfoDTO>();
            for (User item : users) {
                values.add(UsersConverter.doToVO(item));
            }
            mutilResponse.setData(values);
            return mutilResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserIds][system failed][userIds=" + userIds + "]", e);

            mutilResponse.setErrCode(UserConstants.ERROR_OTHER);
            mutilResponse.setErrMessage("system failure");
            return mutilResponse;
        }
    }

    public SingleResponse<Map<Long,UserInfoDTO>> queryMapByUserIds(List<Long> userIds){
        SingleResponse<Map<Long,UserInfoDTO>> singleResponse = new SingleResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(userIds) || userIds.size() > 500) {
            log.error("[UserServiceImpl.queryMapByUserIds][invalid params][userIds={}]", userIds);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            userIds = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());


            List<User> users = iUserRepository.listByIds(userIds);
            singleResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(users)) {
                // 未查到数据
                return singleResponse;
            }
            // 组装VO对象返回
            Map<Long, UserInfoDTO> map = new HashMap<>();
            for (User item : users) {
                map.put(item.getUserId(), UsersConverter.doToVO(item));
            }
            singleResponse.setData(map);
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserIds][system failed][userIds=" + userIds + "]", e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public SingleResponse<UserInfoDTO> queryByUserName(String name, String domain) {
        SingleResponse<UserInfoDTO> singleResponse = new SingleResponse<UserInfoDTO>();
        // 入参校验
        if (StringUtils.isEmpty(name) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserName][invalid params][name={},domain={}]", name, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("uname", name);
            queryWrapper.eq("domain", domain);
            User user = iUserRepository.getOne(queryWrapper);
            // 组装VO对象返回
            singleResponse.setSuccess(Boolean.TRUE);
            singleResponse.setData(UsersConverter.doToVO(user));
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserName][system failed][name=" + name + ",domain=" + domain + "]",
                    e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }

    /**
     * 根据用户名模糊查询，（最多返回10条）
     *
     * @param name   用户名
     * @param domains 用户业务域
     * @param includeDeleted 是否包含 UserStatusEnum 中 IS_DELETED 的用户
     * @return 查询成功返回 {@link UserInfoDTO} 对象,查询失败返回NULL对象
     */
    @Override
    public MultiResponse<UserInfoDTO> fuzzyQueryByUserName(String name, List<String> domains, Boolean includeDeleted) {
        MultiResponse<UserInfoDTO> response = new MultiResponse<>();
        // 入参校验
//        if (StringUtils.isEmpty(name)) {
//            log.error("[UserServiceImpl.fuzzyQueryAllUserByName][invalid params][name={}][includeDeleted={}]", name, includeDeleted);
//            response.setErrCode(UserConstants.ERROR_PARAM);
//            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
//            return response;
//        }
        try {
            // 从DB中查询数据
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            if (!StringUtils.isEmpty(name)) {
                queryWrapper.nested(a -> a.like(User::getUnick, name).or().like(User::getUname, name).or().like(User::getExtra, name));
            }
            if (!CollectionUtils.isEmpty(domains)){
                queryWrapper.in(User::getDomain, domains);
            }
            if (!includeDeleted) {
                queryWrapper.in(User::getStatus, UserStatusEnum.getValuesWithoutDeleted());
            }
            queryWrapper.last("limit 10");
            List<User> userList = iUserRepository.list(queryWrapper);

            // 组装VO对象返回
            List<UserInfoDTO> values = new ArrayList<>();
            if (!CollectionUtils.isEmpty(userList)) {
                for (User item : userList) {
                    values.add(UsersConverter.doToVO(item));
                }
            }
            response.setSuccess(Boolean.TRUE);
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[UserServiceImpl.fuzzyQueryByUserName][system failed][name=" + name + "][includeDeleted=" + includeDeleted + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 根据用户名查询所有业务域下的用户信息
     *
     * @param name 用户名
     * @return 查询成功返回 {@link UserInfoDTO} 对象,查询失败返回NULL对象
     */

    public MultiResponse<UserInfoDTO> queryAllUserByName(String name) {
        MultiResponse<UserInfoDTO> response = new MultiResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(name)) {
            log.error("[UserServiceImpl.queryAllUserByName][invalid params][name={}]", name);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("uname", name);
            List<User> userList = iUserRepository.list(queryWrapper);

            // 组装VO对象返回
            List<UserInfoDTO> values = new ArrayList<>();
            if (!CollectionUtils.isEmpty(userList)) {
                for (User item : userList) {
                    values.add(UsersConverter.doToVO(item));
                }
            }
            response.setSuccess(Boolean.TRUE);
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryAllUserByName][system failed][name=" + name + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    @Override
    public MultiResponse<UserInfoDTO> fuzzyQueryAllUserByName(String name, Boolean includeDeleted) {
        MultiResponse<UserInfoDTO> response = new MultiResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(name)) {
            log.error("[UserServiceImpl.fuzzyQueryAllUserByName][invalid params][name={}][includeDeleted={}]", name, includeDeleted);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            if (!StringUtils.isEmpty(name)) {
                queryWrapper.nested(a -> a.like(User::getUnick, name).or().like(User::getExtra, name));
            }
            if (!includeDeleted) {
                queryWrapper.in(User::getStatus, UserStatusEnum.getValuesWithoutDeleted());
            }
            queryWrapper.last("limit 10");
            List<User> userList = iUserRepository.list(queryWrapper);

            // 组装VO对象返回
            List<UserInfoDTO> values = new ArrayList<>();
            if (!CollectionUtils.isEmpty(userList)) {
                for (User item : userList) {
                    values.add(UsersConverter.doToVO(item));
                }
            }
            response.setSuccess(Boolean.TRUE);
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[UserServiceImpl.fuzzyQueryAllUserByName][system failed][name=" + name + "][includeDeleted=" + includeDeleted + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }


    public MultiResponse<UserInfoDTO> queryByUserNames(List<String> names, String domain) {
        MultiResponse<UserInfoDTO> singleResponse = new MultiResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(names) || names.size() >= 500
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserNames][invalid params][names={},domain={}]", names, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("uname", names);
            queryWrapper.eq("domain", domain);
            List<User> users = iUserRepository.list(queryWrapper);

            singleResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(users)) {
                // 未查到数据
                return singleResponse;
            }
            // 组装VO对象返回
            List<UserInfoDTO> values = new ArrayList<>();
            for (User item : users) {
                values.add(UsersConverter.doToVO(item));
            }
            singleResponse.setData(values);
            return singleResponse;

        } catch (Exception e) {
            log.error(
                    "[UserServiceImpl.queryByUserNames][system failed][names=" + names + ",domain=" + domain + "]",
                    e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public SingleResponse<UserInfoDTO> queryByUserNick(String nick, String domain) {
        SingleResponse<UserInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isBlank(nick) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserNick][invalid params][nick={},domain={}]", nick, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("unick", nick);
            queryWrapper.eq("domain", domain);
            User userDO = iUserRepository.getOne(queryWrapper);
            // 组装VO对象返回
            singleResponse.setSuccess(Boolean.TRUE);
            singleResponse.setData(UsersConverter.doToVO(userDO));
            return singleResponse;

        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserNick][system failed][nick=" + nick + ",domain=" + domain + "]",
                    e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public SingleResponse<UserInfoDTO> queryByUserMobile(String mobile, String areaCode, String domain) {
        SingleResponse<UserInfoDTO> singleResponse = new SingleResponse<UserInfoDTO>();
        // 入参校验
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserMobile][invalid params][mobile={},areaCode={},domain={}]",
                    mobile, areaCode, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", mobile);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            List<UserMobile> usersMobileDOs = iUserMobileRepository.list(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(usersMobileDOs)) {
                // 未查到数据
                return singleResponse;
            }
            // 根据手机号绑定的userId查询用户信息返回
            return queryUserByUserId(usersMobileDOs.get(0).getUserId());
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserMobile][system failed][mobile=" + mobile + ",areaCode="
                    + areaCode + ",domain=" + domain + "]", e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public MultiResponse<UserInfoDTO> queryByUserMobiles(List<String> mobiles, String areaCode, String domain) {
        MultiResponse<UserInfoDTO> multiResponse = new MultiResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(mobiles) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserMobiles][invalid params][mobiles={},areaCode={},domain={}]",
                    mobiles, areaCode, domain);
            multiResponse.setErrCode(UserConstants.ERROR_PARAM);
            multiResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return multiResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("mobile", mobiles);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            List<UserMobile> usersMobiles = iUserMobileRepository.list(queryWrapper);
            multiResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(usersMobiles)) {
                return multiResponse;
            }
            List<Long> userIds = new ArrayList<>();
            for (UserMobile usersMobile : usersMobiles) {
                userIds.add(usersMobile.getUserId());
            }
            // 根据手机号绑定的userId查询用户信息返回
            return queryByUserIds(userIds);
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserMobiles][system failed][mobiles=" + mobiles + ",areaCode="
                    + areaCode + ",domain=" + domain + "]", e);

            multiResponse.setErrCode(UserConstants.ERROR_OTHER);
            multiResponse.setErrMessage("system failure");
            return multiResponse;
        }
    }


    public SingleResponse<UserInfoDTO> queryByUserThirdParty(String thirdId, int thirdType, String domain) {
        SingleResponse<UserInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(thirdId) || (null == ThirdPartyEnum.getThirdPartyByCode(thirdType))
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryByUserThirdParty][invalid params][thirdId={},thirdType={},domain={}]",
                    thirdId, thirdType, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("thirdId", thirdId);
            queryWrapper.eq("thirdType", thirdType);
            queryWrapper.eq("domain", domain);
            UserThirdParty usersThirdPartyDO = iUserThirdPartyRepository.getOne(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (null == usersThirdPartyDO) {
                // 未查到数据
                return singleResponse;
            }
            // 根据三方绑定的userId查询用户信息返回
            return queryUserByUserId(usersThirdPartyDO.getUserId());
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserThirdParty][system failed][thirdId=" + thirdId + ",thirdType="
                    + thirdType + ",domain=" + domain + "]", e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public MultiResponse<UserInfoDTO> queryByUserThirdParties(List<String> thirdIds, int thirdType, String domain) {
        MultiResponse<UserInfoDTO> multiResponse = new MultiResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(thirdIds) || (null == ThirdPartyEnum.getThirdPartyByCode(thirdType))
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error(
                    "[UserServiceImpl.queryByUserThirdParties][invalid params][thirdIds={},thirdType={},domain={}]",
                    thirdIds, thirdType, domain);
            multiResponse.setErrCode(UserConstants.ERROR_PARAM);
            multiResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return multiResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("thirdId", thirdIds);
            queryWrapper.eq("thirdType", thirdType);
            queryWrapper.eq("domain", domain);
            List<UserThirdParty> usersThirdParties = iUserThirdPartyRepository
                    .list(queryWrapper);
            multiResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(usersThirdParties)) {
                return multiResponse;
            }
            List<Long> userIds = new ArrayList<Long>();
            for (UserThirdParty userThirdParty : usersThirdParties) {
                userIds.add(userThirdParty.getUserId());
            }
            // 根据三方绑定的userId查询用户信息返回
            return queryByUserIds(userIds);
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryByUserThirdParties][system failed][thirdIds=" + thirdIds
                    + ",thirdType=" + thirdType + ",domain=" + domain + "]", e);

            multiResponse.setErrCode(UserConstants.ERROR_OTHER);
            multiResponse.setErrMessage("system failure");
            return multiResponse;
        }
    }


    public SingleResponse<Boolean> updateStatus(Long userId, String statusName, boolean addOrRemove) {
        SingleResponse<Boolean> singleResponse = new SingleResponse<>();
        // 入参校验
        if ((userId == null || userId <= 0) || (null == UserStatusEnum.getUsersStatusEnumByName(statusName))) {
            log.error("[UserServiceImpl.updateStatus][invalid params][userId={},statusName={},addOrRemove={}]",
                    userId, statusName, addOrRemove);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setData(Boolean.FALSE);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            User userDO = iUserRepository.getById(userId);
            if (null == userDO) {
                // 如果用户存在,返回用户不存在
                singleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
                singleResponse.setErrMessage("user not found error");
                singleResponse.setData(Boolean.FALSE);
                return singleResponse;
            }
            // 构建最终的状态值
            UserStatusEnum userStatusEnum = UserStatusEnum.getUsersStatusEnumByName(statusName);
            int newUserStatus = UserStatusEnum.genUserStatus(userDO.getStatus(), userStatusEnum.getIndex(),
                    addOrRemove);
            // 更新DB用户状态字段
            UpdateWrapper<User> userUpdateWrapper = new UpdateWrapper<>();
            userUpdateWrapper.eq("userId", userId);
            userUpdateWrapper.set("status", newUserStatus);
            boolean result = iUserRepository.update(userUpdateWrapper);
            if (!result) {
                log.error(
                        "[UserServiceImpl.updateStatus][update user status failure][userId={},statusName={},addOrRemove={}]",
                        userId, statusName, addOrRemove);
                singleResponse.setErrCode(UserConstants.ERROR_OTHER);
                singleResponse.setErrMessage("update user status failed");
                singleResponse.setData(Boolean.FALSE);
                return singleResponse;
            }
            singleResponse.setData(Boolean.TRUE);
            singleResponse.setSuccess(Boolean.TRUE);
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.updateStatus][system failed][userId=" + userId + ",statusName=" + statusName
                    + ",addOrRemove=" + addOrRemove + "]", e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            singleResponse.setData(Boolean.FALSE);
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<ThirdPartyInfoDTO> queryThirdPartyInfoByUserId(long userId) {
        SingleResponse<ThirdPartyInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (userId <= 0) {
            log.error("[UserServiceImpl.queryThirdPartyInfoByUserId][invalid params][userId={}]", userId);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("userId", userId);
            UserThirdParty userThirdParty = iUserThirdPartyRepository.getOne(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (null == userThirdParty) {
                // 未查到数据
                return singleResponse;
            }
            // 根据三方绑定的userId查询用户信息返回
            singleResponse.setData(UsersThirdPartyConverter.doToVO(userThirdParty));
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryThirdPartyInfoByUserId][invalid params][userId={}]", userId);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<Map<Long, ThirdPartyInfoDTO>> queryThirdPartyInfoMapByUserIds(List<Long> userIds, int thirdType) {
        SingleResponse<Map<Long, ThirdPartyInfoDTO>> singleResponse = new SingleResponse<>();
        Map<Long, ThirdPartyInfoDTO> map = new HashMap<>();
        // 入参校验
        if (CollectionUtils.isEmpty(userIds) || userIds.size() > 500) {
            log.error("[UserServiceImpl.queryThirdPartyInfoMapByUserIds][invalid params][userIds={}]", userIds);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            userIds = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId" , userIds);
            queryWrapper.eq("thirdType", thirdType);
            List<UserThirdParty> list = iUserThirdPartyRepository.list(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(list)) {
                // 未查到数据
                singleResponse.setData(map);
                return singleResponse;
            }
            // 组装VO对象返回
            for (UserThirdParty item : list) {
                map.put(item.getUserId(), UsersThirdPartyConverter.doToVO(item));
            }
            singleResponse.setData(map);
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryThirdPartyInfoMapByUserIds][system failed][userIds=" + userIds + "]", e);

            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<ThirdPartyInfoDTO> queryThirdPartyInfoByThirdId2(String thirdId2, int thirdType, String domain) {
        SingleResponse<ThirdPartyInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(thirdId2) || (null == ThirdPartyEnum.getThirdPartyByCode(thirdType))
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[UserServiceImpl.queryThirdPartyInfoByThirdId2][invalid params][thirdId2={},thirdType={},domain={}]",
                    thirdId2, thirdType, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("thirdId2", thirdId2);
            queryWrapper.eq("thirdType", thirdType);
            queryWrapper.eq("domain", domain);
            UserThirdParty userThirdParty = iUserThirdPartyRepository.getOne(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (null == userThirdParty) {
                // 未查到数据
                return singleResponse;
            }
            // 根据三方绑定的userId查询用户信息返回
            singleResponse.setData(UsersThirdPartyConverter.doToVO(userThirdParty));
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryThirdPartyInfoByThirdId2][invalid params][thirdId2={}]", thirdId2);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<Map<String, ThirdPartyInfoDTO>> queryThirdPartyInfoMapByThirdId2s(List<String> thirdId2s, int thirdType, String domain) {
        SingleResponse<Map<String, ThirdPartyInfoDTO>> singleResponse = new SingleResponse<>();
        Map<String, ThirdPartyInfoDTO> map = new HashMap<>();
        // 入参校验
        if (CollectionUtils.isEmpty(thirdId2s) || (null == ThirdPartyEnum.getThirdPartyByCode(thirdType))
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error(
                    "[UserServiceImpl.queryThirdPartyInfoMapByThirdId2s][invalid params][thirdIds={},thirdType={},domain={}]",
                    thirdId2s, thirdType, domain);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            thirdId2s = thirdId2s.stream().filter(Objects::nonNull).collect(Collectors.toList());
            QueryWrapper<UserThirdParty> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("thirdId2", thirdId2s);
            queryWrapper.eq("thirdType", thirdType);
            queryWrapper.eq("domain", domain);
            List<UserThirdParty> list = iUserThirdPartyRepository.list(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(list)) {
                // 未查到数据
                singleResponse.setData(map);
                return singleResponse;
            }
            // 组装VO对象返回
            for (UserThirdParty item : list) {
                map.put(item.getThirdId2(), UsersThirdPartyConverter.doToVO(item));
            }
            singleResponse.setData(map);
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryThirdPartyInfoMapByThirdId2s][system failed][thirdId2s=" + thirdId2s + "]", e);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }


    public SingleResponse<Boolean> updateUserInfo(UserInfoDTO userInfoDTO) {
        SingleResponse<Boolean> SingleResponse = new SingleResponse<>();
        SingleResponse.setData(Boolean.FALSE);
        // 入参校验
        if (userInfoDTO == null || userInfoDTO.getUserId() <= 0 ||
                (StringUtils.isEmpty(userInfoDTO.getUname()) && StringUtils.isEmpty(userInfoDTO.getUnick()) &&
                        StringUtils.isEmpty(userInfoDTO.getAvatar()) &&
                        (null == userInfoDTO.getGender() || null == UserGenderEnum.getUsersGenderEnumByCode(userInfoDTO.getGender())) &&
                        null == userInfoDTO.getExtraInfo())
        ) {
            log.error(
                    "[UserServiceImpl.updateUserInfo][invalid params][userInfoDTO={}]",
                    userInfoDTO);
            SingleResponse.setErrCode(UserConstants.ERROR_PARAM);
            SingleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return SingleResponse;
        }
        try {

            // 从DB中查询数据
            User user = iUserRepository.getById(userInfoDTO.getUserId());
            if (null == user) {
                // 如果用户不存在, 返回用户不存在
                SingleResponse.setErrCode(UserConstants.ERROR_USER_NOT_EXIST);
                SingleResponse.setErrMessage("用户不存在");
                return SingleResponse;
            }
            // 组装用户DO
            User userUpdate = new User();
            userUpdate.setUserId(userInfoDTO.getUserId());
            if (!StringUtils.isEmpty(userInfoDTO.getUname())) {
                if (checkUnameExist(userInfoDTO.getUserId(), userInfoDTO.getUname(), user.getDomain())) {
                    SingleResponse.setErrCode(UserConstants.ERROR_USER_UPDATE);
                    SingleResponse.setErrMessage("用户名已存在");
                    return SingleResponse;
                }
                userUpdate.setUname(userInfoDTO.getUname());
            }
            if (!StringUtils.isEmpty(userInfoDTO.getUnick())) {
                userUpdate.setUnick(userInfoDTO.getUnick());
            }
            if (!StringUtils.isEmpty(userInfoDTO.getAvatar())) {
                userUpdate.setAvatar(StringEscapeUtils.escapeHtml4(userInfoDTO.getAvatar()));
            }
            if ((null != userInfoDTO.getGender()) && (null != UserGenderEnum.getUsersGenderEnumByCode(userInfoDTO.getGender()))) {
                userUpdate.setGender(userInfoDTO.getGender());
            }
            if (!CollectionUtils.isEmpty(userInfoDTO.getExtraInfo())) {
                if (StringUtils.isEmpty(user.getExtra())) {
                    Map<String, Map<String, Object>> extraData = new HashMap<>();
                    extraData.put(user.getDomain(), userInfoDTO.getExtraInfo());
                    userUpdate.setExtra(JSONObject.toJSONString(extraData));
                } else {
                    // 执行json解析
                    Map<String, Map<String, Object>> extraRes = JSON.parseObject(user.getExtra(), Map.class);
                    if (CollectionUtils.isEmpty(extraRes)) {
                        extraRes = new HashMap<>();
                        extraRes.put(user.getDomain(), userInfoDTO.getExtraInfo());
                        userUpdate.setExtra(JSON.toJSONString(extraRes));
                    } else if (CollectionUtils.isEmpty(extraRes.get(user.getDomain()))) {
                        extraRes.put(user.getDomain(), userInfoDTO.getExtraInfo());
                        userUpdate.setExtra(JSON.toJSONString(extraRes));
                    } else {
                        Map<String, Object> domainData = extraRes.get(user.getDomain());
                        for (Map.Entry<String, Object> entry : userInfoDTO.getExtraInfo().entrySet()) {
                            domainData.put(entry.getKey(), entry.getValue());
                        }
                        userUpdate.setExtra(JSON.toJSONString(extraRes));
                    }
                }
            }
            // 更新DB用户状态字段
            userUpdate.setUpdated(CommonUtil.getCurrentSeconds());
            boolean result = iUserRepository.updateById(userUpdate);
            if (!result) {
                log.error(
                        "[UserServiceImpl.updateUserInfo][update user info failure][userInfoDTO={}]",
                        userInfoDTO);
                SingleResponse.setErrCode(UserConstants.ERROR_OTHER);
                SingleResponse.setErrMessage("update user info failed");
                return SingleResponse;
            }

            SingleResponse.setData(Boolean.TRUE);
            SingleResponse.setSuccess(Boolean.TRUE);

            return SingleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.updateUserInfo][system failed][userInfo=" + userInfoDTO + " " + e.getMessage(), e);
            SingleResponse.setErrCode(UserConstants.ERROR_OTHER);
            SingleResponse.setErrMessage("system failure");
            SingleResponse.setData(Boolean.FALSE);
            return SingleResponse;
        }
    }

    @Override
    public SingleResponse<Boolean> updateThirdPartyInfo(ThirdPartyInfoDTO thirdPartyInfoDTO) {
        SingleResponse<Boolean> singleResponse = new SingleResponse<>();
        singleResponse.setData(Boolean.FALSE);
        // 入参校验
        if (thirdPartyInfoDTO == null || thirdPartyInfoDTO.getUserId() <= 0 ||
                StringUtils.isEmpty(thirdPartyInfoDTO.getThirdId()) || StringUtils.isEmpty(thirdPartyInfoDTO.getDomain()) ||
                thirdPartyInfoDTO.getThirdType() == null ||
                        (StringUtils.isEmpty(thirdPartyInfoDTO.getThirdId2()) &&
                        StringUtils.isEmpty(thirdPartyInfoDTO.getThirdName()) &&
                        StringUtils.isEmpty(thirdPartyInfoDTO.getAppId()))
        ) {
            log.error(
                    "[UserServiceImpl.updateThirdPartyInfo][invalid params][thirdPartyInfoDTO={}]",
                    thirdPartyInfoDTO);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
        // 从DB中查询数据
            LambdaQueryWrapper<UserThirdParty> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserThirdParty::getThirdId, thirdPartyInfoDTO.getThirdId())
                    .eq(UserThirdParty::getThirdType, thirdPartyInfoDTO.getThirdType())
                    .eq(UserThirdParty::getDomain, thirdPartyInfoDTO.getDomain())
                    .eq(UserThirdParty::getUserId, thirdPartyInfoDTO.getUserId())
                    .eq(UserThirdParty::getAppId, thirdPartyInfoDTO.getAppId());
            UserThirdParty userThirdParty = iUserThirdPartyRepository.getOne(queryWrapper);
            if (null == userThirdParty) {
                // 如果不存在, 返回
                singleResponse.setErrCode(UserConstants.ERROR_THIRD_PARTY_NOT_EXIST);
                singleResponse.setErrMessage("thirdPartyInfo not found error");
                return singleResponse;
            }
            // 组装用户DO
            UserThirdParty userThirdPartyUpdate = new UserThirdParty();
            userThirdPartyUpdate.setId(userThirdParty.getId());
            if (!StringUtils.isEmpty(thirdPartyInfoDTO.getThirdId2())) {
                userThirdPartyUpdate.setThirdId2(thirdPartyInfoDTO.getThirdId2());
            }
            if (!StringUtils.isEmpty(thirdPartyInfoDTO.getAppId())) {
                userThirdPartyUpdate.setAppId(thirdPartyInfoDTO.getAppId());
            }
            if (!StringUtils.isEmpty(thirdPartyInfoDTO.getThirdName())) {
                userThirdPartyUpdate.setThirdName(thirdPartyInfoDTO.getThirdName());
            }
            userThirdPartyUpdate.setUpdated(CommonUtil.getCurrentSeconds());
            boolean result = iUserThirdPartyRepository.updateById(userThirdPartyUpdate);
            if (!result) {
                log.error(
                        "[UserServiceImpl.updateThirdPartyInfo][update thirdParty info failure][thirdPartyInfoDTO={}]",
                        thirdPartyInfoDTO);
                singleResponse.setErrCode(UserConstants.ERROR_OTHER);
                singleResponse.setErrMessage("update thirdParty info failed");
                return singleResponse;
            }

            singleResponse.setData(Boolean.TRUE);
            singleResponse.setSuccess(Boolean.TRUE);
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.updateThirdPartyInfo][system failed][thirdPartyInfoDTO= " + thirdPartyInfoDTO + " " + e.getMessage(), e);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            singleResponse.setData(Boolean.FALSE);
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<ThirdPartyInfoDTO> queryThirdPartyInfoByThirdIdAndAppId(String thirdId, String appId, int thirdType) {
        SingleResponse<ThirdPartyInfoDTO> singleResponse = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(thirdId) || null == ThirdPartyEnum.getThirdPartyByCode(thirdType)
                || null == thirdPartyAppInfoCacheManager.getByAppIdAndThirdPlatform(appId, ThirdPartyEnum.getThirdPartyByCode(thirdType).getPlatform())) {
            log.error(
                    "[UserServiceImpl.queryThirdPartyInfoByThirdIdAndAppId][invalid params][thirdId={},appId={},thirdType={}]",
                    thirdId, appId, thirdType);
            singleResponse.setErrCode(UserConstants.ERROR_PARAM);
            singleResponse.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return singleResponse;
        }
        try {
            // 从DB中查询数据
            LambdaQueryWrapper<UserThirdParty> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserThirdParty::getThirdId, thirdId)
                    .eq(UserThirdParty::getThirdType, thirdType)
                    .eq(UserThirdParty::getAppId, appId);
            UserThirdParty userThirdParty = iUserThirdPartyRepository.getOne(queryWrapper);
            singleResponse.setSuccess(Boolean.TRUE);
            if (null == userThirdParty) {
                // 未查到数据
                return singleResponse;
            }
            // 根据三方绑定的userId查询用户信息返回
            singleResponse.setData(UsersThirdPartyConverter.doToVO(userThirdParty));
            return singleResponse;
        } catch (Exception e) {
            log.error("[UserServiceImpl.queryThirdPartyInfoByThirdIdAndAppId][system failed][thirdId=" + thirdId + ",appId=" + appId + ",thirdType=" + thirdType + "]", e);
            singleResponse.setErrCode(UserConstants.ERROR_OTHER);
            singleResponse.setErrMessage("system failure");
            return singleResponse;
        }
    }

    @Override
    public SingleResponse<Boolean> deleteThirdPartyInfoByUserId(Long userId) {
        LambdaQueryWrapper<UserThirdParty> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserThirdParty::getUserId, userId);
        iUserThirdPartyRepository.remove(queryWrapper);
        return SingleResponse.buildSuccess(true);
    }


    /**
     * 检查uname-domain是否唯一
     * 同一个userId的情况下，不需要检查唯一索引
     *
     * @return true表示已存在，false表示不存在
     */
    private boolean checkUnameExist(Long userId, String uname, String domain) {
        if (userId == null || StringUtils.isEmpty(uname) || StringUtils.isEmpty(domain)) {
            return false;
        }

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uname", uname);
        queryWrapper.eq("domain", domain);
        User user = iUserRepository.getOne(queryWrapper);
        return user != null && !userId.equals(user.getUserId());
    }

}
