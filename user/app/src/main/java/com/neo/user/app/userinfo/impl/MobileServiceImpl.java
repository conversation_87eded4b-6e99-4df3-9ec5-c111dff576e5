package com.neo.user.app.userinfo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.app.tennant.TenantDomainCacheManager;
import com.neo.user.app.userinfo.converter.UserMobileConverter;
import com.neo.user.client.userinfo.api.MobileService;
import com.neo.user.client.userinfo.dto.UserMobileInfoDTO;
import com.neo.user.domain.constants.UserConstants;
import com.neo.user.domain.entity.UserMobile;
import com.neo.user.client.enums.UserMobileStatusEnum;
import com.neo.user.domain.gateway.IUserMobileRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MobileServiceImpl implements MobileService {

    @Autowired
    private IUserMobileRepository iUserMobileRepository;
    @Autowired
    private TenantDomainCacheManager tenantDomainCacheManager;

    /**
     * 根据用户ID查找手机信息
     *
     * @param userId 用户ID
     * @param domain 用户业务域
     * @return 查询成功返回
     */
    @Override
    public SingleResponse<UserMobileInfoDTO> queryMobileInfoByUserId(Long userId, String domain) {
        SingleResponse<UserMobileInfoDTO> response = new SingleResponse<>();
        // 入参校验
        if ((userId <= 0) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[MobileServiceImpl.queryMobileInfoByUserId][invalid params][userId={},domain={}]", userId,
                    domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId", userId);
            queryWrapper.in("domain", domain);
            queryWrapper.eq("deleted", 0);
            UserMobile queryRes = iUserMobileRepository.getOne(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData(UserMobileConverter.doToVO(queryRes));
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.queryMobileInfoByUserId][query failed][userId=" + userId + ",domain="
                    + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 根据用户ID查找全部domain下手机信息
     *
     * @param userId 用户ID
     * @return list 查询成功返回 {@link UserMobileInfoDTO} 对象列表,查询失败返回NULL对象
     */
    @Override
    public MultiResponse<UserMobileInfoDTO> queryAllMobileInfoByUserId(Long userId) {
        MultiResponse<UserMobileInfoDTO> response = new MultiResponse<>();
        // 入参校验
        if ((userId <= 0)) {
            log.error("[MobileServiceImpl.queryAllMobileInfoByUserId][invalid params][userId={}]", userId);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId", userId);
            queryWrapper.eq("deleted", 0);
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData(UserMobileConverter.doToVOList(queryRes));
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.queryAllMobileInfoByUserId][query failed][userId=" + userId + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 根据手机号查询手机信息
     *
     * @param mobile   手机号码
     * @param areaCode 国际区号(如中国为"86")
     * @param domain   用户业务域
     * @return
     */
    @Override
    public SingleResponse<UserMobileInfoDTO> queryMobileInfoByMobile(String mobile, String areaCode, String domain) {
        SingleResponse<UserMobileInfoDTO> response = new SingleResponse<>();
        // 入参校验
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error(
                    "[MobileServiceImpl.queryMobileInfoByMobile][invalid params][mobile={},areaCode={},domain={}]",
                    mobile, areaCode, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("mobile", mobile);
            queryWrapper.in("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            queryWrapper.eq("deleted", 0);
            UserMobile userMobile = iUserMobileRepository.getOne(queryWrapper);
            // 组装VO对象返回
            response.setSuccess(Boolean.TRUE);
            response.setData(UserMobileConverter.doToVO(userMobile));
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.queryMobileInfoByMobile][query failed][mobile=" + mobile + ",areaCode="
                    + areaCode + ",domain=" + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 根据用户ID批量查询手机信息
     *
     * @param userIds 用户ID列表
     * @param domain  用户业务域 , 不支持domain=all
     * @return 查询成功返回 {@link UserMobileInfoDTO} 对象列表,查询失败返回NULL对象
     */
    @Override
    public MultiResponse<UserMobileInfoDTO> queryMobileInfoByUserIds(List<Long> userIds, String domain) {

        MultiResponse<UserMobileInfoDTO> response = new MultiResponse<UserMobileInfoDTO>();
        // 入参校验,不允许domain=all
        if (CollectionUtils.isEmpty(userIds) || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[MobileServiceImpl.queryMobileInfoByUserId][invalid params][userIds={},domain={}]", userIds,
                    domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("userId", userIds);
            queryWrapper.eq("domain", domain);
            queryWrapper.eq("deleted", 0);
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            response.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(queryRes)) {
                // 未查询到数据
                return response;
            }
            // 查询到了数据,组装VO对象返回
            List<UserMobileInfoDTO> values = new ArrayList<>();
            for (UserMobile item : queryRes) {
                values.add(UserMobileConverter.doToVO(item));
            }
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.queryMobileInfoByUserIds][query failed][userIds=" + userIds + ",domain="
                    + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 根据手机号批量查询手机信息
     *
     * @param mobiles  手机号码
     * @param areaCode 国际区号(如中国为"86")
     * @param domain   用户业务域
     * @return
     */
    @Override
    public MultiResponse<UserMobileInfoDTO> queryMobileInfoByMobiles(List<String> mobiles, String areaCode, String domain) {
        MultiResponse<UserMobileInfoDTO> response = new MultiResponse<>();
        // 入参校验
        if (CollectionUtils.isEmpty(mobiles) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error(
                    "[MobileServiceImpl.queryMobileInfoByMobiles][invalid params][mobiles={},areaCode={},domain={}]",
                    mobiles, areaCode, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            return response;
        }
        try {
            // 从DB中查询数据
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("mobile", mobiles);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            queryWrapper.eq("deleted", 0);
            List<UserMobile> queryRes = iUserMobileRepository.list(queryWrapper);
            response.setSuccess(Boolean.TRUE);
            if (CollectionUtils.isEmpty(queryRes)) {
                // 未查询到数据
                return response;
            }
            // 查询到了数据,组装VO对象返回
            List<UserMobileInfoDTO> values = new ArrayList<>();
            for (UserMobile item : queryRes) {
                values.add(UserMobileConverter.doToVO(item));
            }
            response.setData(values);
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.queryMobileInfoByUserIds][query failed][mobiles=" + mobiles + ",areaCode="
                    + areaCode + ",domain=" + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            return response;
        }
    }

    /**
     * 设置用户绑定手机
     *
     * @param userId   用户ID
     * @param mobile   手机号码
     * @param areaCode 国际区号(如中国为"86")
     * @param domain   用户业务域
     * @param status   手机认证状态 0-未认证，1-已认证
     * @param extra    扩展信息
     * @return true:设置成功,false:设置失败
     */
    @Override
    public SingleResponse<Boolean> setMobile(Long userId, String mobile, String areaCode, String domain, Integer status, Map<String, Object> extra) {
        SingleResponse<Boolean> response = new SingleResponse<>();
        // 入参校验
        if ((userId <= 0) || StringUtils.isEmpty(mobile) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)
                || (null == UserMobileStatusEnum.getUsersMobileStatusEnumByCode(status))) {
            log.error(
                    "[MobileServiceImpl.setMobile][invalid params][userId={},mobile={},areaCode={},domain={},status={}]",
                    userId, mobile, areaCode, domain, status);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            response.setData(Boolean.FALSE);
            return response;
        }
        try {
            boolean result = false;
            // 判断当前号码在当前域有没被绑定
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", mobile);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            queryWrapper.eq("deleted", 0);
            UserMobile userMobile = iUserMobileRepository.getOne(queryWrapper);
            if (userMobile == null) {
                // 构建DB数据
                UserMobile data = new UserMobile();
                data.setUserId(userId);
                data.setMobile(mobile);
                data.setDomain(domain);
                data.setAreaCode(areaCode);
                data.setStatus(status);
                result = iUserMobileRepository.save(data);
            }

            if (!result) {
                log.error(
                        "[MobileServiceImpl.setMobile][set mobile failure][userId={},mobile={},areaCode={},domain={},status={}",
                        userId, mobile, areaCode, domain, status);
                response.setErrCode(UserConstants.ERROR_USER_MOBILE_BIND);
                response.setErrMessage("该手机号已被绑定");
                response.setData(Boolean.FALSE);
                return response;
            }
            response.setData(Boolean.TRUE);
            response.setSuccess(Boolean.TRUE);
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.setMobile][set mobile failure][userId=" + userId + ",mobile=" + mobile
                    + ",areaCode=" + areaCode + ",domain=" + domain + ",status=" + status + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            response.setData(Boolean.FALSE);
            return response;
        }
    }

    /**
     * 解绑用户手机号码
     *
     * @param userId   用户ID
     * @param mobile   手机号码
     * @param areaCode 国际区号(如中国为"86")
     * @param domain   用户业务域 ,若domain=all时解绑MOGUJIE域及其子域下所有手机绑定记录
     * @return
     */
    @Override
    public SingleResponse<Boolean> unbindMobile(Long userId, String mobile, String areaCode, String domain) {
        SingleResponse<Boolean> response = new SingleResponse<>();
        // 入参校验
        if ((userId <= 0) || StringUtils.isEmpty(mobile) || StringUtils.isEmpty(areaCode)
                || null == tenantDomainCacheManager.getSessionDomainByDomain(domain)) {
            log.error("[MobileServiceImpl.unbindMobile][invalid params][userId={},mobile={},areaCode={},domain={}]",
                    userId, mobile, areaCode, domain);
            response.setErrCode(UserConstants.ERROR_PARAM);
            response.setErrMessage(UserConstants.ERROR_PARAM_MESSAGE);
            response.setData(Boolean.FALSE);
            return response;
        }
        try {
            QueryWrapper<UserMobile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("userId", userId);
            queryWrapper.eq("mobile", mobile);
            queryWrapper.eq("areaCode", areaCode);
            queryWrapper.eq("domain", domain);
            queryWrapper.eq("deleted", 0);
            UserMobile userMobile = iUserMobileRepository.getOne(queryWrapper);

            if (userMobile == null) {
                log.error("[MobileServiceImpl.unbindMobile][invalid params][userId={},mobile={},areaCode={},domain={}]",
                        userId, mobile, areaCode, domain);
                response.setErrCode(UserConstants.ERROR_USER_MOBILE_DELETE);
                response.setErrMessage("unbind mobileInfo failed");
                response.setData(Boolean.FALSE);
                return response;
            }
            // 执行解绑手机号
            UpdateWrapper<UserMobile> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("deleted", 1);
            updateWrapper.eq("userId", userId);
            updateWrapper.eq("domain", domain);
            updateWrapper.eq("mobile", mobile);
            boolean result = iUserMobileRepository.update(updateWrapper);

            if (!result) {
                // 未匹配到数据
                response.setErrCode(UserConstants.ERROR_USER_MOBILE_DELETE);
                response.setData(Boolean.FALSE);
                response.setErrMessage("unbind mobileInfo failed");
                return response;
            }
            response.setData(Boolean.TRUE);
            response.setSuccess(Boolean.TRUE);
            return response;
        } catch (Exception e) {
            log.error("[MobileServiceImpl.unbindMobile][unbind mobile failure][userId=" + userId + ",mobile="
                    + mobile + ",areaCode=" + areaCode + ",domain=" + domain + "]", e);

            response.setErrCode(UserConstants.ERROR_OTHER);
            response.setErrMessage("system failure");
            response.setData(Boolean.FALSE);
            return response;
        }
    }
}
