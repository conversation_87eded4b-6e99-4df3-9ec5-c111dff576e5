package com.neo.user.client.tenant.api;

import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.user.client.tenant.dto.DepartmentInfoDTO;
import com.neo.user.client.tenant.dto.UserDepartmentDTO;

import java.util.List;
import java.util.Map;

/**
 * 部门查询
 */
public interface DepartmentService {

    /**
     * 单查（包含已删除）
     * @param tenantId  租户id
     * @param deptId    部门id
     * @return
     */
    public SingleResponse<DepartmentInfoDTO> getDeptInfoById(Long tenantId, Long deptId);

    /**
     * 批量查（包含已删除）
     * @param tenantId  租户id
     * @param deptIds   部门id
     * @return
     */
    public SingleResponse<Map<Long , DepartmentInfoDTO>> getDeptInfoMapByIds(Long tenantId , List<Long> deptIds);

    /**
     * 判断用户是否存在于部门中
     * @param tenantId  租户id
     * @param userId    用户id
     * @param deptIds   部门id列表
     * @param includeSubDept    是否遍历子部门
     * @return
     */
    public SingleResponse<Boolean> checkUserDepartment(Long tenantId, Long userId, List<Long> deptIds, Boolean includeSubDept);

    /**
     * 获取该部门下的所有用户id，不分页
     * @param tenantId  租户id
     * @param deptId    部门id
     * @param includeSubDept    是否遍历子部门
     * @return
     */
    public MultiResponse<Long> getAllUserIdByDeptId(Long tenantId, Long deptId, Boolean includeSubDept);

    /**
     * 获取多个部门下的所有用户id，不分页
     * @param tenantId  租户id
     * @param deptIds   部门id列表
     * @param includeSubDept    是否遍历子部门
     * @return
     */
    public MultiResponse<Long> getAllUserIdByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept);

    /**
     * 查询主管管辖部门id
     * @param tenantId
     * @param leaderUserId
     * @return
     */
    public MultiResponse<Long> getDeptIdsByLeaderId(Long tenantId, Long leaderUserId);

    /**
     * 获取该部门下的所有userId和deptId，不分页
     * @param tenantId
     * @param deptId
     * @param includeSubDept
     * @return
     */
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptId(Long tenantId, Long deptId, Boolean includeSubDept);

    /**
     * 获取多个部门下的所有userId和deptId，不分页
     * @param tenantId
     * @param deptIds
     * @param includeSubDept
     * @return
     */
    public MultiResponse<UserDepartmentDTO> getAllUserDeptByDeptIds(Long tenantId, List<Long> deptIds, Boolean includeSubDept);

    /**
     * 根据extra查询部门id
     * @param tenantId
     * @param extra 部门扩展extra
     * @param includeSubDept 是否包含子部门id
     * @return
     */
    public MultiResponse<Long> getDeptIdsByExtra(Long tenantId, String extra, Boolean includeSubDept);

    /**
     * 查询所有子部门id
     * @param tenantId
     * @param deptId
     * @return
     */
    public MultiResponse<Long> getSubDeptIdsByDeptId(Long tenantId, Long deptId);
}
