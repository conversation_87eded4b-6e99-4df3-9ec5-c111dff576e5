package com.neo.user.client.tenant.dto;

import com.neo.user.client.userinfo.dto.UserInfoDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

import static com.neo.user.client.userinfo.Constants.Constants.USER_STATUS_DELETED;
import static com.neo.user.client.userinfo.Constants.Constants.USER_STATUS_NO_LOGIN;

@Data
public class TenantUserInfoDTO extends UserInfoDTO {

    private String deptName;

    private Long deptId;

    private Long tenantId;

    private String tenantName;

    private String mobile;

//    private String email;

    private List<String> roles;

    public Boolean getIsDeleted(){
        return getStatusList().contains(USER_STATUS_DELETED);
    }

    public Boolean getIsVirtual(){
        return getStatusList().contains(USER_STATUS_NO_LOGIN);
    }

    public String getJobNumber(){
        if (getExtraInfo() != null){
            Map<String, Object> extra = (Map<String, Object>) getExtraInfo().get(getDomain());
            if (extra != null && extra.get("jobNumber") != null){
                return extra.get("jobNumber").toString();
            }
        }
        return "";
    }

//    public String getMobile(){
//        if (getExtraInfo() != null){
//            Map<String, Object> extra = (Map<String, Object>) getExtraInfo().get(getDomain());
//            if (extra != null && extra.get("mobile") != null){
//                return extra.get("mobile").toString();
//            }
//        }
//        return "";
//    }

    public String getEmail(){
        if (getExtraInfo() != null){
            Map<String, Object> extra = (Map<String, Object>) getExtraInfo().get(getDomain());
            if (extra != null && extra.get("email") != null){
                return extra.get("email").toString();
            }
        }
        return "";
    }

}
