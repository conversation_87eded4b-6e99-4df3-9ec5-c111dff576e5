package com.neo.user.infrastructure.gateway.remote.department;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.api.code.GlobalResponseCodeEnum;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.*;
import com.neo.tagcenter.client.rpc.TreeTagReadService;
import com.neo.tagcenter.client.rpc.TreeTagWriteService;
import com.neo.user.domain.entity.DepartmentLeader;
import com.neo.user.domain.entity.User;
import com.neo.user.domain.gateway.IDepartmentLeaderRepository;
import com.neo.user.domain.gateway.IUserRepository;
import com.neo.user.domain.gateway.department.IDepartmentRepository;
import com.neo.user.domain.gateway.department.dto.DepartmentDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.neo.user.domain.constants.UserConstants.DEPARTMENT_TAG;

@Service
public class DepartmentRepositoryImpl implements IDepartmentRepository {

    @Autowired
    private TreeTagReadService treeTagReadService;

    @Autowired
    private TreeTagWriteService treeTagWriteService;

    @Autowired
    private IUserRepository iUserRepository;

    @Autowired
    private IDepartmentLeaderRepository iDepartmentLeaderRepository;

    @Override
    public MultiResponse<DepartmentDTO> getAllDepartment(Long tenantId) {
        BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
        option.setQueryChild(true);
        MultiResponse<TagLeafInfoDto> tagLeafInfoDtoMultiResponse = treeTagReadService.queryTagLeafInfoByBusinessDomainAndTagDomain(tenantId.intValue(),DEPARTMENT_TAG, option);
        if (tagLeafInfoDtoMultiResponse.isSuccess() && !CollectionUtils.isEmpty(tagLeafInfoDtoMultiResponse.getData())){
            List<TagLeafInfoDto> tagLeafInfoDtos = tagLeafInfoDtoMultiResponse.getData();
            // 查leader
            List<Long> deptIds = tagLeafInfoDtos.stream().map(TagLeafInfoDto::getId).toList();
            Map<Long, User> leaderMap = getLeaderMap(tenantId, deptIds);
            List<DepartmentDTO> departmentDTOS = tagLeafInfoDtos.stream().map(tagLeafInfo -> {
                DepartmentDTO department = new DepartmentDTO();
                department.setDeptId(tagLeafInfo.getId());
                department.setName(tagLeafInfo.getName());
                department.setParentId(tagLeafInfo.getParentId());
                department.setTenantId(tenantId);
                department.setExtra(tagLeafInfo.getCode());
                if (leaderMap.containsKey(tagLeafInfo.getId())) {
                    User leader = leaderMap.get(tagLeafInfo.getId());
                    department.setLeaderId(leader.getUserId());
                    department.setLeaderName(leader.getUnick());
                }
                return department;
            }).collect(Collectors.toList());
            List<DepartmentDTO> list = convertToTree(departmentDTOS, 0L);
            return MultiResponse.of(list);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public MultiResponse<Long> getAllSubDeptIds(Long tenantId, Long deptId) {
        BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
        option.setQueryChild(true);
        MultiResponse<TagLeafInfoDto> tagLeafInfoDtoMultiResponse = treeTagReadService.queryTagLeafInfoById(deptId, option);
        if (tagLeafInfoDtoMultiResponse.isSuccess()){
            List<TagLeafInfoDto> tagLeafInfoDtos = tagLeafInfoDtoMultiResponse.getData();
            List<Long> deptIds = tagLeafInfoDtos.stream().map(TagLeafInfoDto::getId).collect(Collectors.toList());
            return MultiResponse.of(deptIds);
        }
        return MultiResponse.of(new ArrayList<>());
    }

    @Override
    public SingleResponse<Map<Long, String>> queryDepartmentNameMap(Long tenantId, List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)){
            return SingleResponse.buildSuccess(new HashMap<>());
        }
        Map<Long, String> map = new HashMap<>();
        TreeTagQueryOption option = new TreeTagQueryOption();
        option.setTagLeafIds(deptIds);
        MultiResponse<TagLeafInfoDto> response = treeTagReadService.queryTagLeafInfo(option);
        if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())){
            List<TagLeafInfoDto> tagLeafInfoDtos = response.getData();
            map = tagLeafInfoDtos.stream().collect(Collectors.toMap(TagLeafInfoDto::getId, TagLeafInfoDto::getName));
        }
        return SingleResponse.buildSuccess(map);
    }

    @Override
    public SingleResponse<DepartmentDTO> get(Long tenantId, Long deptId, Boolean includeDeleted) {
        BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
        option.setIncludeDeleted(includeDeleted);
        MultiResponse<TagLeafInfoDto> exists = treeTagReadService.queryTagLeafInfoById(deptId, option);
        if (!exists.isSuccess() || CollectionUtils.isEmpty(exists.getData())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        TagLeafInfoDto tagLeafInfoDto = exists.getData().get(0);
        if (!tagLeafInfoDto.getTagDomain().equals(DEPARTMENT_TAG) || !tagLeafInfoDto.getBusinessDomain().equals(tenantId.intValue())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setDeptId(deptId);
        departmentDTO.setName(tagLeafInfoDto.getName());
        departmentDTO.setParentId(tagLeafInfoDto.getParentId());
        departmentDTO.setTenantId(tenantId);
        departmentDTO.setExtra(tagLeafInfoDto.getCode());
        Map<Long, User> leaderMap = getLeaderMap(tenantId, Collections.singletonList(deptId));
        if (leaderMap.containsKey(deptId)) {
            User leader = leaderMap.get(deptId);
            departmentDTO.setLeaderId(leader.getUserId());
            departmentDTO.setLeaderName(leader.getUnick());
        }
        return SingleResponse.buildSuccess(departmentDTO);
    }

    @Override
    public SingleResponse<Boolean> init(Long tenantId, String domain) {
        TagBusinessDomainParam tagBusinessDomainParam = new TagBusinessDomainParam();
        tagBusinessDomainParam.setCode(tenantId.intValue());
        tagBusinessDomainParam.setName(domain);
        return treeTagWriteService.addBusinessDomain(tagBusinessDomainParam);
    }

    @Override
    public MultiResponse<DepartmentDTO> multiGet(Long tenantId, List<Long> deptIds, Boolean includeDeleted) {
        TreeTagQueryOption option = new TreeTagQueryOption();
        option.setTagLeafIds(deptIds);
        option.setIncludeDeleted(includeDeleted);
        MultiResponse<TagLeafInfoDto> exists = treeTagReadService.queryTagLeafInfo(option);
        if (!exists.isSuccess() || CollectionUtils.isEmpty(exists.getData())){
            return MultiResponse.of(new ArrayList<>());
        }
        Map<Long, User> leaderMap = getLeaderMap(tenantId, deptIds);
        List<DepartmentDTO> list = new ArrayList<>();
        exists.getData().forEach(
                a -> {
                    if (a.getTagDomain().equals(DEPARTMENT_TAG) && a.getBusinessDomain().equals(tenantId.intValue())){
                        DepartmentDTO departmentDTO = new DepartmentDTO();
                        departmentDTO.setDeptId(a.getId());
                        departmentDTO.setName(a.getName());
                        departmentDTO.setParentId(a.getParentId());
                        departmentDTO.setTenantId(tenantId);
                        departmentDTO.setExtra(a.getCode());
                        if (leaderMap.containsKey(a.getId())){
                            departmentDTO.setLeaderId(leaderMap.get(a.getId()).getUserId());
                            departmentDTO.setLeaderName(leaderMap.get(a.getId()).getUnick());
                        }
                        list.add(departmentDTO);
                    }
                }
        );
        return MultiResponse.of(list);
    }

    @Override
    public SingleResponse<Boolean> add(DepartmentDTO departmentDTO) {
        TreeTagSaveParam treeTagSaveParam = new TreeTagSaveParam();
        treeTagSaveParam.setParentId(departmentDTO.getParentId());
        treeTagSaveParam.setName(departmentDTO.getName());
        treeTagSaveParam.setTagDomain(DEPARTMENT_TAG);
        treeTagSaveParam.setBusinessDomain(departmentDTO.getTenantId().intValue());
        treeTagSaveParam.setCode(departmentDTO.getExtra());
        treeTagSaveParam.setCreator(0L);

        SingleResponse<Long> res = treeTagWriteService.addTagLeafInfo(treeTagSaveParam);
        if (!res.isSuccess()){
            return SingleResponse.buildFailure(res.getErrCode(), res.getErrMessage());
        }
        if (departmentDTO.getLeaderId() != null && departmentDTO.getLeaderId() != 0L){
            DepartmentLeader departmentLeader = new DepartmentLeader();
            departmentLeader.setDeptId(res.getData());
            departmentLeader.setLeaderId(departmentDTO.getLeaderId());
            departmentLeader.setTenantId(departmentDTO.getTenantId());
            iDepartmentLeaderRepository.save(departmentLeader);
        }
        return SingleResponse.buildSuccess(true);
    }

    @Override
    public SingleResponse<Boolean> update(DepartmentDTO departmentDTO) {
        MultiResponse<TagLeafInfoDto> exists = treeTagReadService.queryTagLeafInfoById(departmentDTO.getDeptId(), new BaseTreeTagQueryOption());
        // 校验tenantId
        if (!exists.isSuccess() || CollectionUtils.isEmpty(exists.getData())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        TagLeafInfoDto tagLeafInfoDto = exists.getData().get(0);
        if (!tagLeafInfoDto.getBusinessDomain().equals(departmentDTO.getTenantId().intValue()) || !tagLeafInfoDto.getTagDomain().equals(DEPARTMENT_TAG)){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
/*        if (!departmentDTO.getParentId().equals(tagLeafInfoDto.getParentId())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR.getCode(), "暂不支持修改父级部门");
        }*/
        TreeTagUpdateParam treeTagUpdateParam = new TreeTagUpdateParam();
        treeTagUpdateParam.setTagLeafId(departmentDTO.getDeptId());
        treeTagUpdateParam.setName(departmentDTO.getName());
        treeTagUpdateParam.setCode(departmentDTO.getExtra());
        treeTagUpdateParam.setParentId(departmentDTO.getParentId());
        SingleResponse<Boolean> res = treeTagWriteService.updateTagLeaf(treeTagUpdateParam);
        if (!res.isSuccess()){
            return SingleResponse.buildFailure(res.getErrCode(), res.getErrMessage());
        }
        if (departmentDTO.getLeaderId() == null || departmentDTO.getLeaderId() == 0L){
            deleteOldLeaders(departmentDTO.getTenantId(), departmentDTO.getDeptId(), null);
        } else {
            LambdaQueryWrapper<DepartmentLeader> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DepartmentLeader::getTenantId, departmentDTO.getTenantId());
            queryWrapper.eq(DepartmentLeader::getDeptId, departmentDTO.getDeptId());
            queryWrapper.eq(DepartmentLeader::getLeaderId, departmentDTO.getLeaderId());
            queryWrapper.eq(DepartmentLeader::getIsDeleted, 0);
            Optional<DepartmentLeader> opt = iDepartmentLeaderRepository.getOneOpt(queryWrapper);
            if (opt.isEmpty()){
                DepartmentLeader newLeader = new DepartmentLeader();
                newLeader.setDeptId(departmentDTO.getDeptId());
                newLeader.setLeaderId(departmentDTO.getLeaderId());
                newLeader.setTenantId(departmentDTO.getTenantId());
                iDepartmentLeaderRepository.save(newLeader);
                deleteOldLeaders(departmentDTO.getTenantId(), departmentDTO.getDeptId(), departmentDTO.getLeaderId());
            }
        }
        return SingleResponse.buildSuccess(true);
    }

    private void deleteOldLeaders(Long tenantId , Long deptId, Long newLeaderId) {
        LambdaQueryWrapper<DepartmentLeader> oldWrapper = new LambdaQueryWrapper<>();
        oldWrapper.eq(DepartmentLeader::getTenantId, tenantId);
        oldWrapper.eq(DepartmentLeader::getDeptId, deptId);
        if (newLeaderId != null && newLeaderId != 0L){
            oldWrapper.ne(DepartmentLeader::getLeaderId, newLeaderId);
        }
        oldWrapper.eq(DepartmentLeader::getIsDeleted,0);
        DepartmentLeader oldLeader = new DepartmentLeader();
        oldLeader.setIsDeleted(1);
        iDepartmentLeaderRepository.update(oldLeader,oldWrapper);
    }

    @Override
    public SingleResponse<Boolean> delete(Long tenantId, Long deptId) {
        MultiResponse<TagLeafInfoDto> exists = treeTagReadService.queryTagLeafInfoById(deptId, new BaseTreeTagQueryOption());
        // 校验tenantId
        if (!exists.isSuccess() || CollectionUtils.isEmpty(exists.getData())){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        TagLeafInfoDto tagLeafInfoDto = exists.getData().get(0);
        if (!tagLeafInfoDto.getBusinessDomain().equals(tenantId.intValue()) || !tagLeafInfoDto.getTagDomain().equals(DEPARTMENT_TAG)){
            return SingleResponse.buildFailure(GlobalResponseCodeEnum.PARAM_ERROR);
        }
        SingleResponse<Boolean> res = treeTagWriteService.delTagLeaf(deptId);
        if (!res.isSuccess()){
            return SingleResponse.buildFailure(res.getErrCode(), res.getErrMessage());
        }
        LambdaQueryWrapper<DepartmentLeader> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentLeader::getTenantId, deptId);
        queryWrapper.eq(DepartmentLeader::getDeptId, deptId);
        queryWrapper.eq(DepartmentLeader::getIsDeleted, 0);
        DepartmentLeader oldLeader = new DepartmentLeader();
        oldLeader.setIsDeleted(1);
        iDepartmentLeaderRepository.update(oldLeader,queryWrapper);
        return res;
    }

    private List<DepartmentDTO> convertToTree(List<DepartmentDTO> departments, Long rootDeptId) {
        // 创建一个映射，用于快速查找每个部门的子部门
        Map<Long, List<DepartmentDTO>> childrenMap = new HashMap<>();
        for (DepartmentDTO department : departments) {
            Long parentId = department.getParentId();
            List<DepartmentDTO> children = childrenMap.getOrDefault(parentId, new ArrayList<>());
            children.add(department);
            childrenMap.put(parentId, children);
        }
        // 递归构建树形结构
        return buildTree(departments, childrenMap, rootDeptId); // 从顶级部门开始构建
    }

    private List<DepartmentDTO> buildTree(List<DepartmentDTO> departments, Map<Long, List<DepartmentDTO>> childrenMap, Long parentId) {
        List<DepartmentDTO> tree = new ArrayList<>();
        List<DepartmentDTO> children = childrenMap.get(parentId);
        if (children != null) {
            for (DepartmentDTO child : children) {
                child.setChildren(buildTree(departments, childrenMap, child.getDeptId()));
                tree.add(child);
            }
        }
        return tree;
    }

    private Map<Long, User> getUserMap(List<Long> userIds) {
        Map<Long, User> userMap = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)){
            return userMap;
        }
        List<User> users = iUserRepository.listByIds(userIds);
        return users.stream().collect(Collectors.toMap(User::getUserId, a -> a));
    }

    private Map<Long, User> getLeaderMap(Long tenantId, List<Long> deptIds) {
        Map<Long, User> leaderMap = new HashMap<>();
        if (CollectionUtils.isEmpty(deptIds)){
            return leaderMap;
        }
        LambdaQueryWrapper<DepartmentLeader> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentLeader::getTenantId, tenantId);
        queryWrapper.in(DepartmentLeader::getDeptId, deptIds);
        queryWrapper.eq(DepartmentLeader::getIsDeleted, 0);
        List<DepartmentLeader> list = iDepartmentLeaderRepository.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)){
            List<Long> leaderIds = list.stream().map(DepartmentLeader::getLeaderId).toList();
            Map<Long, User> userMap = getUserMap(leaderIds);
            list.forEach(a -> {
                leaderMap.put(a.getDeptId(), userMap.get(a.getLeaderId()));
            });
        }
        return leaderMap;
    }

    @Override
    public Integer getDeptPos(Long deptId){
        MultiResponse<TagLeafInfoDto> response = treeTagReadService.queryTagLeafInfoById(deptId, new BaseTreeTagQueryOption());
        if (response.isSuccess()){
            return response.getData().get(0).getPos();
        }
        return 0;
    }

    @Override
    public MultiResponse<Long> getDeptIdsByExtra(Long tenantId, String extra) {
        // TODO 入参要增加tenantId 和 DEPT
        MultiResponse<TagLeafInfoDto> tagLeafInfoDtoMultiResponse = treeTagReadService.queryTagLeafInfoByTagCode(extra, new BaseTreeTagQueryOption());
        if (!tagLeafInfoDtoMultiResponse.isSuccess()){
            return MultiResponse.of(new ArrayList<>());
        }
        List<Long> list = tagLeafInfoDtoMultiResponse.getData().stream().map(TagLeafInfoDto::getId).toList();
        return MultiResponse.of(list);
    }
}
