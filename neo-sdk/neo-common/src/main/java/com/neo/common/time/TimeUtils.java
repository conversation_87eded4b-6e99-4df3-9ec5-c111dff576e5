/**
 * mogujie.com Inc.
 * Copyright (c) 2010-2015 All Rights Reserved.
 */
package com.neo.common.time;

import cn.hutool.core.date.DateUtil;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @version $Id: DateUtil.java, v 0.1 2015年12月24日 下午10:12:14 nalan Exp $
 */
public class TimeUtils {
    public static final String Y = "yyyy";
    public static final String M = "MM";
    public static final String D = "dd";
    public static final String YM = "yyyyMM";
    public static final String YMD = "yyyyMMdd";
    public static final String YMD4SHOW = "yyyy-MM-dd";
    public static final String YMD_HMS = "yyyyMMdd HHmmss";
    public static final String YMDHMS = "yyyyMMddHHmmss";
    public static final String YMDHMS4SHOW = "yyyy-MM-dd HH:mm:ss";
    public static final String HMS = "HHmmss";

    public static final int oneDayMillisTime = 86400000;
    public static final int oneDayTimestamp = 86400;

    /**
     * 获取格式化时间
     *
     * @param time
     * @param format
     * @return
     */
    public static String formatTime(long time, String format) {
        DateTimeFormatter fmt = DateTimeFormat.forPattern(format);
        return fmt.print(time);
    }

    /**
     * 获取往前指定周期起始时间(昨天,上一小时,上一周)
     *
     * @param field
     * @param amount
     * @return 秒级时间戳
     */
    public static long calculateTimestamp(int field, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.add(field, amount);
        String format = TimeUtils.YMD;
        switch (field) {
            case Calendar.HOUR:
                format = "yyyyMMddHH";
                break;
            case Calendar.DATE:
                format = "yyyyMMdd";
                break;
            case Calendar.MONTH:
                format = "yyyyMM";
                break;
            default:
        }
        String lastTimeStr = new SimpleDateFormat(format).format(cal.getTime());
        return TimeUtils.parseTime(lastTimeStr, format);
    }

    /**
     * 获取格式化时间
     *
     * @param time
     * @param format
     * @return
     */
    public static String formatTime(int time, String format) {
        DateTimeFormatter fmt = DateTimeFormat.forPattern(format);
        return fmt.print(time * 1000L);
    }

    /**
     * 获取秒级时间戳
     *
     * @param time   日期格式
     * @param format 格式的模板
     * @return
     */
    public static long parseTime(String time, String format) {
        DateTimeFormatter fmt = DateTimeFormat.forPattern(format);
        return fmt.parseMillis(time) / 1000;
    }

    /**
     * 获取毫秒级时间戳
     *
     * @param time   时间 日期格式
     * @param format 格式 格式的模板
     * @return
     */
    public static long parseMillisTime(String time, String format) {
        DateTimeFormatter fmt = DateTimeFormat.forPattern(format);
        return fmt.parseMillis(time);
    }

    /**
     * 获取今天的日期
     *
     * @return 日期
     */
    public static String getTodayDate() {
        return TimeUtils.formatTime(TimeUtils.getCurrentTime(), TimeUtils.YMD);
    }

    /**
     * 获取今天的日期
     *
     * @return 日期
     */
    public static String getYesterdayDate() {
        return TimeUtils.formatTime(TimeUtils.getCurrentTime() - oneDayTimestamp, TimeUtils.YMD);
    }

    /**
     * 获取今天零时秒级时间戳
     *
     * @return 时间戳
     */
    public static long getTodayTimestamp() {
        return DateUtil.beginOfDay(DateUtil.date()).getTime() / 1000;
    }

    /**
     * 获取当前系统秒级时间戳
     *
     * @return 当前系统时间
     */
    public static long getCurrentTime() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 从毫秒转换秒
     *
     * @param millisTime 时间戳
     * @return
     */
    public static long getTimeByMillis(long millisTime) {
        return millisTime / 1000;
    }

}
