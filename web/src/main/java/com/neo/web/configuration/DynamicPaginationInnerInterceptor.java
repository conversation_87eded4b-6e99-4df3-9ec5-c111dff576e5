package com.neo.web.configuration;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 动态分页拦截器，根据当前数据源自动选择合适的分页方言
 * 
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
public class DynamicPaginationInnerInterceptor extends PaginationInnerInterceptor {

    public DynamicPaginationInnerInterceptor() {
        // 默认使用MySQL方言
        super(DbType.MYSQL);
    }

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        // 动态设置数据库类型
        DbType dbType = determineDbType(executor);
        if (dbType != null) {
            this.setDbType(dbType);
            log.debug("动态设置分页方言为: {}", dbType);
        }
        
        super.beforeQuery(executor, ms, parameter, rowBounds, resultHandler, boundSql);
    }

    /**
     * 根据当前数据源确定数据库类型
     */
    private DbType determineDbType(Executor executor) {
        try {
            // 获取当前数据源名称
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.debug("当前数据源: {}", currentDataSource);
            
            if (currentDataSource != null) {
                if (currentDataSource.startsWith("sqlserver")) {
                    return DbType.SQL_SERVER2005;
                } else if ("mysql".equals(currentDataSource) || currentDataSource == null) {
                    return DbType.MYSQL;
                }
            }
            
            // 如果无法从上下文获取，尝试从连接获取
            Connection connection = executor.getTransaction().getConnection();
            if (connection != null) {
                String url = connection.getMetaData().getURL();
                if (url.contains("sqlserver")) {
                    return DbType.SQL_SERVER2005;
                } else if (url.contains("mysql")) {
                    return DbType.MYSQL;
                }
            }
        } catch (Exception e) {
            log.warn("无法确定数据库类型，使用默认MySQL方言", e);
        }
        
        // 默认返回MySQL
        return DbType.MYSQL;
    }
}
