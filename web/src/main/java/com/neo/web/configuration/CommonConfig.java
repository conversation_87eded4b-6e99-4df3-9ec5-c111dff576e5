package com.neo.web.configuration;
/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2024/3/7 11:24
 */

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.scan.StandardJarScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class CommonConfig {

    @Value("${neo.application.env}")
    private String env;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> customizer() {
        log.info("初始化配置加载webapp配置！！！！！");
        log.info("当前环境->>>>>" + env);
        return factory -> factory.addContextCustomizers(context -> ((StandardJarScanner) context.getJarScanner()).setScanManifest(false));
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 添加动态数据源分页拦截器，根据当前数据源自动选择合适的分页方言
        DynamicPaginationInnerInterceptor dynamicPaginationInterceptor = new DynamicPaginationInnerInterceptor();
        interceptor.addInnerInterceptor(dynamicPaginationInterceptor);

        log.info("MyBatis Plus动态分页拦截器配置完成，支持MySQL和SQL Server自动切换");

        return interceptor;
    }

}
